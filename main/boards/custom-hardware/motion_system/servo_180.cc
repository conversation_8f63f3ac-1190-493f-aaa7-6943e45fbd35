#include "servo_180.h"
#include <esp_log.h>
#include <esp_log_level.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <string.h>

#define SERVO_PWM_FREQ 50  // 50Hz for standard servos
#undef TAG
#define TAG "Servo180"

// Default SG90 180-degree servo PWM range (in microseconds)
static const servo_pwm_range_t default_pwm_range = {
    .min_pulse_width_us = 500,  // 0.5ms - 0 degrees
    .max_pulse_width_us = 2500, // 2.5ms - 180 degrees
};

Servo180::Servo180(gpio_num_t gpio, mcpwm_timer_handle_t timer, const servo_pwm_range_t* pwm_range, bool reverse)
    : gpio_(gpio), timer_(timer), current_angle_(-1), initialized_(false), reverse_(reverse)
{
    ESP_LOGI(TAG, "Servo180 constructor: GPIO=%d, Timer=%p, Reverse=%d", gpio_, timer_, reverse_);
    if (gpio_ == GPIO_NUM_NC || timer_ == nullptr) {
        ESP_LOGE(TAG, "Invalid arguments: GPIO=%d, Timer=%p", gpio_, timer_);
        return;
    }
    if (pwm_range) {
        pwm_range_ = *pwm_range;
        ESP_LOGI(TAG, "Using custom PWM range: min=%lu, max=%lu", pwm_range_.min_pulse_width_us, pwm_range_.max_pulse_width_us);
    } else {
        pwm_range_ = default_pwm_range;
        ESP_LOGI(TAG, "Using default PWM range: min=%lu, max=%lu", pwm_range_.min_pulse_width_us, pwm_range_.max_pulse_width_us);
    }
    esp_err_t err = setup_pwm();
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "MCPWM initialization failed, GPIO: %d, error: %d", gpio_, err);
    } else {
        ESP_LOGI(TAG, "MCPWM initialized successfully, GPIO: %d, Reverse: %d, oper_=%p, cmpr_=%p, gen_=%p", gpio_, reverse_, oper_, cmpr_, gen_);
        initialized_ = true;
        set_angle(90); // Set initial angle after successful initialization
    }
}

Servo180::~Servo180() {
    ESP_LOGI(TAG, "Servo180 destructor: GPIO=%d, oper_=%p, cmpr_=%p, gen_=%p, initialized_=%d", gpio_, oper_, cmpr_, gen_, initialized_);
    if (initialized_) {
        if (gen_) mcpwm_del_generator(gen_);
        if (cmpr_) mcpwm_del_comparator(cmpr_);
        if (oper_) mcpwm_del_operator(oper_);
        ESP_LOGI(TAG, "MCPWM resources cleaned up, GPIO: %d", gpio_);
    }
}

esp_err_t Servo180::setup_pwm() {
    esp_err_t err;
    ESP_LOGI(TAG, "[setup_pwm] GPIO=%d, group_id=0, timer_=%p", gpio_, timer_);
    initialized_ = false;
    mcpwm_operator_config_t oper_config = { .group_id = 0 };
    err = mcpwm_new_operator(&oper_config, &oper_);
    ESP_LOGI(TAG, "[setup_pwm] mcpwm_new_operator: oper_=%p, err=%d", oper_, err);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create MCPWM operator, GPIO: %d, error: %d", gpio_, err);
        return err;
    }
    err = mcpwm_operator_connect_timer(oper_, timer_);
    ESP_LOGI(TAG, "[setup_pwm] mcpwm_operator_connect_timer: oper_=%p, timer_=%p, err=%d", oper_, timer_, err);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to connect timer to operator, GPIO: %d, error: %d", gpio_, err);
        mcpwm_del_operator(oper_); oper_ = nullptr;
        return err;
    }
    mcpwm_comparator_config_t cmpr_config = {}; cmpr_config.flags.update_cmp_on_tez = true;
    err = mcpwm_new_comparator(oper_, &cmpr_config, &cmpr_);
    ESP_LOGI(TAG, "[setup_pwm] mcpwm_new_comparator: cmpr_=%p, err=%d", cmpr_, err);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create comparator, GPIO: %d, error: %d", gpio_, err);
        mcpwm_del_operator(oper_); oper_ = nullptr;
        return err;
    }
    mcpwm_generator_config_t gen_config = { .gen_gpio_num = gpio_ };
    err = mcpwm_new_generator(oper_, &gen_config, &gen_);
    ESP_LOGI(TAG, "[setup_pwm] mcpwm_new_generator: gen_=%p, err=%d", gen_, err);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create GPIO generator, GPIO: %d, error: %d", gpio_, err);
        mcpwm_del_comparator(cmpr_); mcpwm_del_operator(oper_); cmpr_ = nullptr; oper_ = nullptr;
        return err;
    }
    // Set initial angle to a neutral position (e.g., 90 degrees)
    // set_angle(90); // Moved to constructor
    err = mcpwm_generator_set_action_on_timer_event(gen_, MCPWM_GEN_TIMER_EVENT_ACTION(MCPWM_TIMER_DIRECTION_UP, MCPWM_TIMER_EVENT_EMPTY, MCPWM_GEN_ACTION_HIGH));
    ESP_LOGI(TAG, "[setup_pwm] mcpwm_generator_set_action_on_timer_event: gen_=%p, err=%d", gen_, err);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set timer event action, GPIO: %d, error: %d", gpio_, err);
        mcpwm_del_generator(gen_); mcpwm_del_comparator(cmpr_); mcpwm_del_operator(oper_); gen_ = nullptr; cmpr_ = nullptr; oper_ = nullptr;
        return err;
    }
    err = mcpwm_generator_set_action_on_compare_event(gen_, MCPWM_GEN_COMPARE_EVENT_ACTION(MCPWM_TIMER_DIRECTION_UP, cmpr_, MCPWM_GEN_ACTION_LOW));
    ESP_LOGI(TAG, "[setup_pwm] mcpwm_generator_set_action_on_compare_event: gen_=%p, cmpr_=%p, err=%d", gen_, cmpr_, err);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set comparator event action, GPIO: %d, error: %d", gpio_, err);
        mcpwm_del_generator(gen_); mcpwm_del_comparator(cmpr_); mcpwm_del_operator(oper_); gen_ = nullptr; cmpr_ = nullptr; oper_ = nullptr;
        return err;
    }
    ESP_LOGI(TAG, "MCPWM setup complete, GPIO: %d, oper_=%p, cmpr_=%p, gen_=%p", gpio_, oper_, cmpr_, gen_);
    return ESP_OK;
}

void Servo180::set_angle(int angle) {
    ESP_LOGI(TAG, "set_angle: gpio=%d, initialized_=%d, reverse=%d, angle=%d", gpio_, initialized_, reverse_, angle);
    if (!initialized_) {
        ESP_LOGW(TAG, "Servo not initialized, cannot set angle: gpio=%d", gpio_);
        return;
    }

    // Stop the servo before setting a new angle to prevent jitter
    stop();

    int final_angle = angle;
    if (reverse_) {
        final_angle = 180 - angle;
    }
    if (final_angle < 0) final_angle = 0;
    if (final_angle > 180) final_angle = 180;

    current_angle_ = final_angle;
    uint32_t pulse_width_us = pwm_range_.min_pulse_width_us + (pwm_range_.max_pulse_width_us - pwm_range_.min_pulse_width_us) * final_angle / 180;
    
    ESP_LOGI(TAG, "set_angle: gpio=%d, angle=%d, pulse_width_us=%lu, oper_=%p, cmpr_=%p, gen_=%p", gpio_, final_angle, pulse_width_us, oper_, cmpr_, gen_);
    esp_err_t err = mcpwm_comparator_set_compare_value(cmpr_, pulse_width_us);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set comparator value: gpio=%d, err=%d, pulse_width_us=%lu", gpio_, err, pulse_width_us);
        return;
    }
    // Enable the generator to start the PWM signal
    mcpwm_generator_set_actions_on_timer_event(gen_, 
        MCPWM_GEN_TIMER_EVENT_ACTION(MCPWM_TIMER_DIRECTION_UP, MCPWM_TIMER_EVENT_EMPTY, MCPWM_GEN_ACTION_HIGH),
        MCPWM_GEN_TIMER_EVENT_ACTION_END());
}

void Servo180::stop() {
    if (!initialized_) {
        return;
    }
    // Disable the generator to stop the PWM signal
    mcpwm_generator_set_actions_on_timer_event(gen_, 
        MCPWM_GEN_TIMER_EVENT_ACTION(MCPWM_TIMER_DIRECTION_UP, MCPWM_TIMER_EVENT_EMPTY, MCPWM_GEN_ACTION_LOW),
        MCPWM_GEN_TIMER_EVENT_ACTION_END());
}

int Servo180::get_angle() const {
    return current_angle_;
}

gpio_num_t Servo180::get_gpio() const {
    return gpio_;
}
