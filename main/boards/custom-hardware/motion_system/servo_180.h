#pragma once
#include "driver/mcpwm_prelude.h"
#include "driver/gpio.h"
#include "esp_err.h"

typedef struct {
    uint32_t min_pulse_width_us; // 0度对应的脉冲宽度（微秒）
    uint32_t max_pulse_width_us; // 180度对应的脉冲宽度（微秒）
} servo_pwm_range_t;

class Servo180 {
public:
    Servo180(gpio_num_t gpio, mcpwm_timer_handle_t timer, const servo_pwm_range_t* pwm_range = nullptr, bool reverse = false);
    ~Servo180();
    esp_err_t setup_pwm();
    void set_angle(int angle);
    void stop();
    int get_angle() const;
    gpio_num_t get_gpio() const;

private:
    gpio_num_t gpio_;
    mcpwm_timer_handle_t timer_;
    mcpwm_oper_handle_t oper_;
    mcpwm_cmpr_handle_t cmpr_;
    mcpwm_gen_handle_t gen_;
    int current_angle_;
    servo_pwm_range_t pwm_range_;
    bool initialized_;
    bool reverse_;
};