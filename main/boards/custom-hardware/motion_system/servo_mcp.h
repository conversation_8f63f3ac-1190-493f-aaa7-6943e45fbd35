#pragma once

#include "servo_180.h"
#include "mcp_server.h"
#include <string>
#include <esp_log.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/mcpwm_prelude.h"

class ServoMcpController {
public:
    ServoMcpController(gpio_num_t left_gpio, gpio_num_t right_gpio);
    ~ServoMcpController();

    // 直接控制接口
    bool SetAngle(const std::string& target, int angle);
    bool Wave(const std::string& target = "both");
    bool Raise(const std::string& target = "both");
    bool Lower(const std::string& target = "both");
    bool Reset(const std::string& target = "both");

    // 预设动作
    bool PerformGreeting();
    bool PerformGoodbye();
    bool PerformCelebration();
    bool PerformThinking();

    // 状态查询
    bool IsInitialized() const { return initialized_; }
    int GetLeftAngle() const;
    int GetRightAngle() const;

private:
    mcpwm_timer_handle_t shared_timer_;
    Servo180* left_servo_;
    Servo180* right_servo_;
    bool initialized_;

    // 内部辅助函数
    std::string NormalizeTarget(const std::string& target);
    bool IsValidTarget(const std::string& target);
    void ExecuteSequence(const std::vector<std::pair<std::string, int>>& sequence, int delay_ms = 500);
};