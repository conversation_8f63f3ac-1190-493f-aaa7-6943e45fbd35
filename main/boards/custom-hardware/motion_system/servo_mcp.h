#pragma once

#include "servo_180.h"
#include "mcp_server.h"
#include <string>
#include <esp_log.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/mcpwm_prelude.h"

class ServoMcpController {
public:
    ServoMcpController(gpio_num_t left_gpio, gpio_num_t right_gpio);
    ~ServoMcpController();

private:
    mcpwm_timer_handle_t shared_timer_;
    Servo180* left_servo_;
    Servo180* right_servo_;
    bool initialized_;
};