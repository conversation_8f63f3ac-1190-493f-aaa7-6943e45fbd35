#include "servo_mcp.h"
#include "servo_180.h"
#include "mcp_server.h"
#include <cJSON.h>
#include <string>
#include <esp_log.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/mcpwm_prelude.h"

#define TAG "ServoMCP"

// 移动到类的成员函数中

ServoMcpController::ServoMcpController(gpio_num_t left_gpio, gpio_num_t right_gpio)
    : shared_timer_(nullptr), left_servo_(nullptr), right_servo_(nullptr), initialized_(false) {
    
    mcpwm_timer_config_t timer_config = {
        .group_id = 0,
        .clk_src = MCPWM_TIMER_CLK_SRC_DEFAULT,
        .resolution_hz = 1000000, // 1MHz, 1us resolution
        .count_mode = MCPWM_TIMER_COUNT_MODE_UP,
        .period_ticks = 20000,    // 20ms period (50Hz)
    };
    esp_err_t err = mcpwm_new_timer(&timer_config, &shared_timer_);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create MCPWM timer: %d", err);
        return;
    }
    
    err = mcpwm_timer_enable(shared_timer_);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to enable MCPWM timer: %d", err);
        mcpwm_del_timer(shared_timer_);
        shared_timer_ = nullptr;
        return;
    }
    
    err = mcpwm_timer_start_stop(shared_timer_, MCPWM_TIMER_START_NO_STOP);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start MCPWM timer: %d", err);
        mcpwm_timer_disable(shared_timer_);
        mcpwm_del_timer(shared_timer_);
        shared_timer_ = nullptr;
        return;
    }
    
    ESP_LOGI(TAG, "MCPWM timer initialized successfully, frequency: 50Hz");
    
    ESP_LOGI(TAG, "Creating left arm servo, GPIO: %d", left_gpio);
    left_servo_ = new Servo180(left_gpio, shared_timer_, nullptr, false);
    ESP_LOGI(TAG, "Creating right arm servo, GPIO: %d", right_gpio);
    right_servo_ = new Servo180(right_gpio, shared_timer_, nullptr, true);
    
    if (left_servo_ && right_servo_) {
        initialized_ = true;
        ESP_LOGI(TAG, "Servo controller initialized successfully - Left GPIO: %d, Right GPIO: %d", left_gpio, right_gpio);
    } else {
        ESP_LOGE(TAG, "Failed to create servo objects - Left: %p, Right: %p", left_servo_, right_servo_);
        return;
    }
    
    auto& mcp_server = McpServer::GetInstance();

    mcp_server.AddTool("self.servo.set_angle",
        "Set servo angle",
        PropertyList({
            Property("target", kPropertyTypeString),
            Property("angle", kPropertyTypeInteger)
        }),
        [this](const PropertyList& properties) -> ReturnValue {
            if (!initialized_) {
                ESP_LOGW(TAG, "Servo controller not initialized");
                return false;
            }
            std::string target = NormalizeTarget(properties["target"].value<std::string>());
            int angle = properties["angle"].value<int>();
            if (!IsValidTarget(target)) {
                ESP_LOGW(TAG, "Invalid parameters: target=%s angle=%d", target.c_str(), angle);
                return false;
            }
            ESP_LOGI(TAG, "Servo set_angle: target=%s angle=%d", target.c_str(), angle);
            
            if (target == "left") {
                left_servo_->set_angle(angle);
            } else if (target == "right") {
                right_servo_->set_angle(angle);
            } else if (target == "both") {
                left_servo_->set_angle(angle);
                right_servo_->set_angle(angle);
            }
            return true;
        });

    // 添加挥手工具
    mcp_server.AddTool("self.servo.wave",
        "Make servo wave gesture",
        PropertyList({
            Property("target", kPropertyTypeString)
        }),
        [this](const PropertyList& properties) -> ReturnValue {
            if (!initialized_) return false;
            std::string target = "both";
            try {
                target = properties["target"].value<std::string>();
            } catch (const std::runtime_error&) {
                // 使用默认值 "both"
            }
            return Wave(target);
        });

    // 添加预设动作工具
    mcp_server.AddTool("self.servo.greeting",
        "Perform greeting gesture",
        PropertyList(std::vector<Property>{}),
        [this](const PropertyList& properties) -> ReturnValue {
            return PerformGreeting();
        });

    mcp_server.AddTool("self.servo.goodbye",
        "Perform goodbye gesture",
        PropertyList(std::vector<Property>{}),
        [this](const PropertyList& properties) -> ReturnValue {
            return PerformGoodbye();
        });

    mcp_server.AddTool("self.servo.celebration",
        "Perform celebration gesture",
        PropertyList(std::vector<Property>{}),
        [this](const PropertyList& properties) -> ReturnValue {
            return PerformCelebration();
        });

    mcp_server.AddTool("self.servo.thinking",
        "Perform thinking gesture",
        PropertyList(std::vector<Property>{}),
        [this](const PropertyList& properties) -> ReturnValue {
            return PerformThinking();
        });

    mcp_server.AddTool("self.servo.reset",
        "Reset servo to neutral position",
        PropertyList({
            Property("target", kPropertyTypeString)
        }),
        [this](const PropertyList& properties) -> ReturnValue {
            if (!initialized_) return false;
            std::string target = "both";
            try {
                target = properties["target"].value<std::string>();
            } catch (const std::runtime_error&) {
                // 使用默认值 "both"
            }
            return Reset(target);
        });
}

ServoMcpController::~ServoMcpController() {
    if (left_servo_) {
        delete left_servo_;
        left_servo_ = nullptr;
    }
    if (right_servo_) {
        delete right_servo_;
        right_servo_ = nullptr;
    }

    if (shared_timer_) {
        mcpwm_timer_disable(shared_timer_);
        mcpwm_del_timer(shared_timer_);
        shared_timer_ = nullptr;
    }
}

// 内部辅助函数实现
std::string ServoMcpController::NormalizeTarget(const std::string& target) {
    if (target == "both_arms" || target == "arm" || target == "both" || target == "hand" || target == "both_hands") return "both";
    if (target == "right_arm" || target == "right" || target == "right_hand") return "right";
    if (target == "left_arm" || target == "left" || target == "left_hand") return "left";
    return target;
}

bool ServoMcpController::IsValidTarget(const std::string& target) {
    std::string t = NormalizeTarget(target);
    return t == "left" || t == "right" || t == "both";
}

// 直接控制接口实现
bool ServoMcpController::SetAngle(const std::string& target, int angle) {
    if (!initialized_) {
        ESP_LOGW(TAG, "Servo controller not initialized");
        return false;
    }

    std::string normalized_target = NormalizeTarget(target);
    if (!IsValidTarget(normalized_target)) {
        ESP_LOGW(TAG, "Invalid target: %s", target.c_str());
        return false;
    }

    ESP_LOGI(TAG, "Setting angle: target=%s angle=%d", normalized_target.c_str(), angle);

    if (normalized_target == "left") {
        left_servo_->set_angle(angle);
    } else if (normalized_target == "right") {
        right_servo_->set_angle(angle);
    } else if (normalized_target == "both") {
        left_servo_->set_angle(angle);
        right_servo_->set_angle(angle);
    }

    return true;
}

bool ServoMcpController::Wave(const std::string& target) {
    ESP_LOGI(TAG, "Performing wave gesture: %s", target.c_str());
    std::vector<std::pair<std::string, int>> sequence = {
        {target, 90},   // 中间位置
        {target, 45},   // 向一边
        {target, 135},  // 向另一边
        {target, 45},   // 再次向一边
        {target, 135},  // 再次向另一边
        {target, 90}    // 回到中间
    };
    ExecuteSequence(sequence, 300);
    return true;
}

bool ServoMcpController::Raise(const std::string& target) {
    ESP_LOGI(TAG, "Raising arms: %s", target.c_str());
    return SetAngle(target, 180);  // 举起
}

bool ServoMcpController::Lower(const std::string& target) {
    ESP_LOGI(TAG, "Lowering arms: %s", target.c_str());
    return SetAngle(target, 0);    // 放下
}

bool ServoMcpController::Reset(const std::string& target) {
    ESP_LOGI(TAG, "Resetting arms: %s", target.c_str());
    return SetAngle(target, 90);   // 中间位置
}

// 预设动作实现
bool ServoMcpController::PerformGreeting() {
    ESP_LOGI(TAG, "Performing greeting gesture");
    std::vector<std::pair<std::string, int>> sequence = {
        {"both", 90},   // 初始位置
        {"right", 135}, // 右手举起
        {"right", 90},  // 右手放下
        {"right", 135}, // 右手再次举起
        {"right", 90}   // 右手放下
    };
    ExecuteSequence(sequence, 500);
    return true;
}

bool ServoMcpController::PerformGoodbye() {
    ESP_LOGI(TAG, "Performing goodbye gesture");
    return Wave("both");
}

bool ServoMcpController::PerformCelebration() {
    ESP_LOGI(TAG, "Performing celebration gesture");
    std::vector<std::pair<std::string, int>> sequence = {
        {"both", 180},  // 双手举起
        {"both", 90},   // 放下
        {"both", 180},  // 再次举起
        {"both", 90}    // 放下
    };
    ExecuteSequence(sequence, 400);
    return true;
}

bool ServoMcpController::PerformThinking() {
    ESP_LOGI(TAG, "Performing thinking gesture");
    std::vector<std::pair<std::string, int>> sequence = {
        {"left", 120},  // 左手托腮
        {"right", 60}   // 右手放低
    };
    ExecuteSequence(sequence, 800);
    return true;
}

// 状态查询实现
int ServoMcpController::GetLeftAngle() const {
    if (!initialized_ || !left_servo_) {
        return -1;
    }
    return left_servo_->get_angle();
}

int ServoMcpController::GetRightAngle() const {
    if (!initialized_ || !right_servo_) {
        return -1;
    }
    return right_servo_->get_angle();
}

// 执行动作序列
void ServoMcpController::ExecuteSequence(const std::vector<std::pair<std::string, int>>& sequence, int delay_ms) {
    for (const auto& step : sequence) {
        SetAngle(step.first, step.second);
        vTaskDelay(pdMS_TO_TICKS(delay_ms));
    }
}