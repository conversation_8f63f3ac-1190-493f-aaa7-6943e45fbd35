#include "servo_mcp.h"
#include "servo_180.h"
#include "mcp_server.h"
#include <cJSON.h>
#include <string>
#include <esp_log.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/mcpwm_prelude.h"

#define TAG "ServoMCP"

static std::string normalize_target(const std::string& target) {
    if (target == "both_arms" || target == "arm" || target == "both" || target == "hand" || target == "both_hands") return "both";
    if (target == "right_arm" || target == "right" || target == "right_hand") return "right";
    if (target == "left_arm" || target == "left" || target == "left_hand") return "left";
    return target;
}

static bool is_valid_target(const std::string& target) {
    std::string t = normalize_target(target);
    return t == "left" || t == "right" || t == "both";
}

ServoMcpController::ServoMcpController(gpio_num_t left_gpio, gpio_num_t right_gpio)
    : shared_timer_(nullptr), left_servo_(nullptr), right_servo_(nullptr), initialized_(false) {
    
    mcpwm_timer_config_t timer_config = {
        .group_id = 0,
        .clk_src = MCPWM_TIMER_CLK_SRC_DEFAULT,
        .resolution_hz = 1000000, // 1MHz, 1us resolution
        .count_mode = MCPWM_TIMER_COUNT_MODE_UP,
        .period_ticks = 20000,    // 20ms period (50Hz)
    };
    esp_err_t err = mcpwm_new_timer(&timer_config, &shared_timer_);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create MCPWM timer: %d", err);
        return;
    }
    
    err = mcpwm_timer_enable(shared_timer_);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to enable MCPWM timer: %d", err);
        mcpwm_del_timer(shared_timer_);
        shared_timer_ = nullptr;
        return;
    }
    
    err = mcpwm_timer_start_stop(shared_timer_, MCPWM_TIMER_START_NO_STOP);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start MCPWM timer: %d", err);
        mcpwm_timer_disable(shared_timer_);
        mcpwm_del_timer(shared_timer_);
        shared_timer_ = nullptr;
        return;
    }
    
    ESP_LOGI(TAG, "MCPWM timer initialized successfully, frequency: 50Hz");
    
    ESP_LOGI(TAG, "Creating left arm servo, GPIO: %d", left_gpio);
    left_servo_ = new Servo180(left_gpio, shared_timer_, nullptr, false);
    ESP_LOGI(TAG, "Creating right arm servo, GPIO: %d", right_gpio);
    right_servo_ = new Servo180(right_gpio, shared_timer_, nullptr, true);
    
    if (left_servo_ && right_servo_) {
        initialized_ = true;
        ESP_LOGI(TAG, "Servo controller initialized successfully - Left GPIO: %d, Right GPIO: %d", left_gpio, right_gpio);
    } else {
        ESP_LOGE(TAG, "Failed to create servo objects - Left: %p, Right: %p", left_servo_, right_servo_);
        return;
    }
    
    auto& mcp_server = McpServer::GetInstance();

    mcp_server.AddTool("self.servo.set_angle",
        "Set servo angle",
        PropertyList({
            Property("target", kPropertyTypeString),
            Property("angle", kPropertyTypeInteger)
        }),
        [this](const PropertyList& properties) -> ReturnValue {
            if (!initialized_) {
                ESP_LOGW(TAG, "Servo controller not initialized");
                return false;
            }
            std::string target = normalize_target(properties["target"].value<std::string>());
            int angle = properties["angle"].value<int>();
            if (!is_valid_target(target)) {
                ESP_LOGW(TAG, "Invalid parameters: target=%s angle=%d", target.c_str(), angle);
                return false;
            }
            ESP_LOGI(TAG, "Servo set_angle: target=%s angle=%d", target.c_str(), angle);
            
            if (target == "left") {
                left_servo_->set_angle(angle);
            } else if (target == "right") {
                right_servo_->set_angle(angle);
            } else if (target == "both") {
                left_servo_->set_angle(angle);
                right_servo_->set_angle(angle);
            }
            return true;
        });
}

ServoMcpController::~ServoMcpController() {
    if (left_servo_) {
        delete left_servo_;
        left_servo_ = nullptr;
    }
    if (right_servo_) {
        delete right_servo_;
        right_servo_ = nullptr;
    }
    
    if (shared_timer_) {
        mcpwm_timer_disable(shared_timer_);
        mcpwm_del_timer(shared_timer_);
        shared_timer_ = nullptr;
    }
}