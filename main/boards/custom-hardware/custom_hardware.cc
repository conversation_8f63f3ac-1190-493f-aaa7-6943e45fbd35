#include "wifi_board.h"

#ifdef __cplusplus
extern "C" {
#endif
#include "eyes/eyes_controller.h"
#ifdef __cplusplus
}
#endif
#include "audio_codecs/no_audio_codec.h"
#include "display/lcd_display.h"
#include "system_reset.h"
#include "application.h"
#include "button.h"
#include "config.h"
#include "mcp_server.h"
#include "lamp_controller.h"
#include "iot/thing_manager.h"
#include "led/single_led.h"
#include "motion_system/servo_mcp.h"

#include <wifi_station.h>
#include <esp_log.h>
#include <driver/i2c_master.h>
#include <esp_lcd_panel_vendor.h>
#include <esp_lcd_panel_io.h>
#include <esp_lcd_panel_ops.h>
#include <driver/spi_common.h>

#if defined(LCD_TYPE_ILI9341_SERIAL)
#include "esp_lcd_ili9341.h"
#endif

#if defined(LCD_TYPE_GC9A01_SERIAL)
#include "esp_lcd_gc9a01.h"
static const gc9a01_lcd_init_cmd_t gc9107_lcd_init_cmds[] = {
    //  {cmd, { data }, data_size, delay_ms}
    {0xfe, (uint8_t[]){0x00}, 0, 0},
    {0xef, (uint8_t[]){0x00}, 0, 0},
    {0xb0, (uint8_t[]){0xc0}, 1, 0},
    {0xb1, (uint8_t[]){0x80}, 1, 0},
    {0xb2, (uint8_t[]){0x27}, 1, 0},
    {0xb3, (uint8_t[]){0x13}, 1, 0},
    {0xb6, (uint8_t[]){0x19}, 1, 0},
    {0xb7, (uint8_t[]){0x05}, 1, 0},
    {0xac, (uint8_t[]){0xc8}, 1, 0},
    {0xab, (uint8_t[]){0x0f}, 1, 0},
    {0x3a, (uint8_t[]){0x05}, 1, 0},
    {0xb4, (uint8_t[]){0x04}, 1, 0},
    {0xa8, (uint8_t[]){0x08}, 1, 0},
    {0xb8, (uint8_t[]){0x08}, 1, 0},
    {0xea, (uint8_t[]){0x02}, 1, 0},
    {0xe8, (uint8_t[]){0x2A}, 1, 0},
    {0xe9, (uint8_t[]){0x47}, 1, 0},
    {0xe7, (uint8_t[]){0x5f}, 1, 0},
    {0xc6, (uint8_t[]){0x21}, 1, 0},
    {0xc7, (uint8_t[]){0x15}, 1, 0},
    {0xf0,
    (uint8_t[]){0x1D, 0x38, 0x09, 0x4D, 0x92, 0x2F, 0x35, 0x52, 0x1E, 0x0C,
                0x04, 0x12, 0x14, 0x1f},
    14, 0},
    {0xf1,
    (uint8_t[]){0x16, 0x40, 0x1C, 0x54, 0xA9, 0x2D, 0x2E, 0x56, 0x10, 0x0D,
                0x0C, 0x1A, 0x14, 0x1E},
    14, 0},
    {0xf4, (uint8_t[]){0x00, 0x00, 0xFF}, 3, 0},
    {0xba, (uint8_t[]){0xFF, 0xFF}, 2, 0},
};
#endif
 
#ifndef TAG
#define TAG "CompactWifiBoardLCD"
#endif

LV_FONT_DECLARE(font_puhui_16_4);
LV_FONT_DECLARE(font_awesome_16_4);

class CompactWifiBoardLCD : public WifiBoard {
private:
 
    Button boot_button_;
    LcdDisplay* display_;

    // 舵机控制器作为成员变量，保证生命周期
    ServoMcpController servo_ctrl;

    


 
    void InitializeButtons() {
        boot_button_.OnClick([this]() {
            auto& app = Application::GetInstance();
            if (app.GetDeviceState() == kDeviceStateStarting && !WifiStation::GetInstance().IsConnected()) {
                ResetWifiConfiguration();
            }
            app.ToggleChatState();
        });
    }

    // 物联网初始化，添加对 AI 可见设备
    void InitializeIot() {
#if CONFIG_IOT_PROTOCOL_XIAOZHI
        auto& thing_manager = iot::ThingManager::GetInstance();
        thing_manager.AddThing(iot::CreateThing("Speaker"));
        thing_manager.AddThing(iot::CreateThing("Screen"));
        thing_manager.AddThing(iot::CreateThing("Lamp"));
#elif CONFIG_IOT_PROTOCOL_MCP
        static LampController lamp(LAMP_GPIO);
        InitializeEyesControl();
#endif
    }

    // 初始化眼睛表情控制的MCP工具
    void InitializeEyesControl() {
        auto& mcp_server = McpServer::GetInstance();

        // 设置眼睛表情
        mcp_server.AddTool("self.eyes.set_emotion",
            "Set eyes emotion",
            PropertyList({
                Property("emotion", kPropertyTypeString)
            }),
            [](const PropertyList& properties) -> ReturnValue {
                std::string emotion = properties["emotion"].value<std::string>();
                eye_state_t state = EYE_STATE_NORMAL;

                if (emotion == "happy") state = EYE_STATE_HAPPY;
                else if (emotion == "angry") state = EYE_STATE_ANGRY;
                else if (emotion == "surprised") state = EYE_STATE_SURPRISED;
                else if (emotion == "sleepy") state = EYE_STATE_SLEEPY;
                else if (emotion == "normal") state = EYE_STATE_NORMAL;

                eyes_set_emotion(state);
                ESP_LOGI("EyesControl", "Set emotion: %s", emotion.c_str());
                return true;
            });

        // 眨眼控制
        mcp_server.AddTool("self.eyes.blink",
            "Make eyes blink",
            PropertyList(std::vector<Property>{}),
            [](const PropertyList& properties) -> ReturnValue {
                eyes_blink();
                ESP_LOGI("EyesControl", "Eyes blink");
                return true;
            });

        // 眼球移动
        mcp_server.AddTool("self.eyes.look_at",
            "Move eyes to look at direction",
            PropertyList({
                Property("x", kPropertyTypeInteger),
                Property("y", kPropertyTypeInteger)
            }),
            [](const PropertyList& properties) -> ReturnValue {
                int x = properties["x"].value<int>();
                int y = properties["y"].value<int>();
                eyes_look_at(x, y);
                ESP_LOGI("EyesControl", "Look at: (%d, %d)", x, y);
                return true;
            });

        // 心跳效果
        mcp_server.AddTool("self.eyes.heartbeat",
            "Create heartbeat effect",
            PropertyList(std::vector<Property>{}),
            [](const PropertyList& properties) -> ReturnValue {
                eyes_heartbeat();
                ESP_LOGI("EyesControl", "Heartbeat effect");
                return true;
            });

        // 闪烁效果
        mcp_server.AddTool("self.eyes.sparkle",
            "Create sparkle effect",
            PropertyList(std::vector<Property>{}),
            [](const PropertyList& properties) -> ReturnValue {
                eyes_sparkle();
                ESP_LOGI("EyesControl", "Sparkle effect");
                return true;
            });
    }

public:
    CompactWifiBoardLCD()
        : boot_button_(BOOT_BUTTON_GPIO),
          servo_ctrl(SERVO_LEFT_ARM_PIN, SERVO_RIGHT_ARM_PIN) // 使用配置文件中的舵机引脚
    {
        init_eyes_display(); // Initialize the eyes display system
        InitializeButtons();
        InitializeIot();
        if (DISPLAY_BACKLIGHT_PIN != GPIO_NUM_NC) {
            GetBacklight()->RestoreBrightness();
        }
    }

    virtual Led* GetLed() override {
        static SingleLed led(BUILTIN_LED_GPIO);
        return &led;
    }

    virtual AudioCodec* GetAudioCodec() override {
#ifdef AUDIO_I2S_METHOD_SIMPLEX
        static NoAudioCodecSimplex audio_codec(AUDIO_INPUT_SAMPLE_RATE, AUDIO_OUTPUT_SAMPLE_RATE,
            AUDIO_I2S_SPK_GPIO_BCLK, AUDIO_I2S_SPK_GPIO_LRCK, AUDIO_I2S_SPK_GPIO_DOUT, AUDIO_I2S_MIC_GPIO_SCK, AUDIO_I2S_MIC_GPIO_WS, AUDIO_I2S_MIC_GPIO_DIN);
#else
        static NoAudioCodecDuplex audio_codec(AUDIO_INPUT_SAMPLE_RATE, AUDIO_OUTPUT_SAMPLE_RATE,
            AUDIO_I2S_GPIO_BCLK, AUDIO_I2S_GPIO_WS, AUDIO_I2S_GPIO_DOUT, AUDIO_I2S_GPIO_DIN);
#endif
        return &audio_codec;
    }

    virtual Display* GetDisplay() override {
        return display_;
    }

    virtual Backlight* GetBacklight() override {
        if (DISPLAY_BACKLIGHT_PIN != GPIO_NUM_NC) {
            static PwmBacklight backlight(DISPLAY_BACKLIGHT_PIN, false);
            return &backlight;
        }
        return nullptr;
    }
};

DECLARE_BOARD(CompactWifiBoardLCD);
