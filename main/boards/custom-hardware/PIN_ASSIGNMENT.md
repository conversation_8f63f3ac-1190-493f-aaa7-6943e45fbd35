# ESP32-S3 引脚分配详细说明

## 引脚分配总览

| 功能分类 | 引脚编号 | 引脚名称 | 用途说明 | 备注 |
|---------|---------|---------|---------|------|
| **双屏系统** | GPIO 19 | SCK | SPI时钟线 | 双屏共享 |
| | GPIO 20 | MOSI | SPI数据输出 | 双屏共享 |
| | GPIO 21 | DC | 数据/命令控制 | 双屏共享 |
| | GPIO 1 | RST | 复位信号 | 双屏共享 |
| | GPIO 2 | CS_LEFT | 左屏片选 | 独立控制 |
| | GPIO 45 | CS_RIGHT | 右屏片选 | 独立控制 |
| **舵机系统** | GPIO 9 | SERVO_LEFT | 左臂舵机PWM | MCPWM控制 |
| | GPIO 10 | SERVO_RIGHT | 右臂舵机PWM | MCPWM控制 |
| **音频系统** | GPIO 4 | MIC_WS | 麦克风字时钟 | I2S输入 |
| | GPIO 5 | MIC_SCK | 麦克风位时钟 | I2S输入 |
| | GPIO 6 | MIC_DIN | 麦克风数据输入 | I2S输入 |
| | GPIO 7 | SPK_DOUT | 扬声器数据输出 | I2S输出 |
| | GPIO 15 | SPK_BCLK | 扬声器位时钟 | I2S输出 |
| | GPIO 16 | SPK_LRCK | 扬声器左右时钟 | I2S输出 |
| **基础功能** | GPIO 0 | BOOT | 启动按钮 | 内置上拉 |
| | GPIO 48 | LED | 内置LED | 状态指示 |
| | GPIO 18 | LAMP | 测试灯控制 | MCP测试 |

## 详细配置说明

### 双屏眼睛表情系统

#### SPI总线配置
```c
// SPI2总线配置
#define LCD_HOST SPI2_HOST
#define DUAL_SCREEN_SCK       GPIO_NUM_19    // SPI时钟
#define DUAL_SCREEN_MOSI      GPIO_NUM_20    // SPI数据输出  
#define DUAL_SCREEN_DC        GPIO_NUM_21    // 数据/命令控制
#define DUAL_SCREEN_RST       GPIO_NUM_1     // 复位引脚
```

#### 片选信号配置
```c
#define DUAL_SCREEN_CS_LEFT   GPIO_NUM_2     // 左屏片选
#define DUAL_SCREEN_CS_RIGHT  GPIO_NUM_45    // 右屏片选
```

#### 引脚特性
- **共享引脚**: SCK、MOSI、DC、RST由双屏共享，节省引脚资源
- **独立片选**: 每个屏幕有独立的CS引脚，支持独立控制
- **高速SPI**: 支持高达80MHz的SPI时钟频率
- **DMA支持**: 使用DMA传输，减少CPU负载

### 舵机控制系统

#### MCPWM配置
```c
#define SERVO_LEFT_ARM_PIN  GPIO_NUM_9   // 左臂舵机
#define SERVO_RIGHT_ARM_PIN GPIO_NUM_10  // 右臂舵机
```

#### PWM参数
- **频率**: 50Hz (20ms周期)
- **分辨率**: 1MHz (1μs精度)
- **脉宽范围**: 500μs - 2500μs
- **角度范围**: 0° - 180°

#### 引脚特性
- **硬件PWM**: 使用ESP32-S3的MCPWM外设
- **高精度**: 1μs分辨率确保精确角度控制
- **低抖动**: 硬件PWM减少信号抖动
- **同步控制**: 共享定时器实现双舵机同步

### 音频系统

#### I2S配置 (Simplex模式)
```c
// 麦克风输入 (I2S RX)
#define AUDIO_I2S_MIC_GPIO_WS   GPIO_NUM_4   // 字时钟
#define AUDIO_I2S_MIC_GPIO_SCK  GPIO_NUM_5   // 位时钟  
#define AUDIO_I2S_MIC_GPIO_DIN  GPIO_NUM_6   // 数据输入

// 扬声器输出 (I2S TX)  
#define AUDIO_I2S_SPK_GPIO_DOUT GPIO_NUM_7   // 数据输出
#define AUDIO_I2S_SPK_GPIO_BCLK GPIO_NUM_15  // 位时钟
#define AUDIO_I2S_SPK_GPIO_LRCK GPIO_NUM_16  // 左右时钟
```

#### 音频参数
- **采样率**: 输入16kHz，输出24kHz
- **位深度**: 16位
- **声道**: 单声道
- **缓冲**: DMA双缓冲

## 引脚冲突分析

### 已使用引脚汇总
```
GPIO 0  - BOOT按钮 (系统保留)
GPIO 1  - 双屏RST (输出)
GPIO 2  - 左屏CS (输出)  
GPIO 4  - 麦克风WS (I2S)
GPIO 5  - 麦克风SCK (I2S)
GPIO 6  - 麦克风DIN (I2S)
GPIO 7  - 扬声器DOUT (I2S)
GPIO 9  - 左臂舵机 (PWM)
GPIO 10 - 右臂舵机 (PWM)
GPIO 15 - 扬声器BCLK (I2S)
GPIO 16 - 扬声器LRCK (I2S)
GPIO 18 - 测试灯 (输出)
GPIO 19 - 双屏SCK (SPI)
GPIO 20 - 双屏MOSI (SPI)
GPIO 21 - 双屏DC (输出)
GPIO 45 - 右屏CS (输出)
GPIO 48 - 内置LED (输出)
```

### 可用引脚
```
GPIO 3, 8, 11, 12, 13, 14, 17, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47
```

### 特殊引脚说明
- **GPIO 0**: 启动模式控制，需要上拉电阻
- **GPIO 45/46**: 高速引脚，适合高频信号
- **GPIO 19/20**: 支持高速SPI，已用于双屏
- **GPIO 1**: 复位引脚，低电平有效

## 扩展建议

### 传感器扩展
推荐使用以下引脚添加传感器：
```c
#define ULTRASONIC_TRIG_PIN  GPIO_NUM_11  // 超声波触发
#define ULTRASONIC_ECHO_PIN  GPIO_NUM_12  // 超声波回声
#define TEMP_SENSOR_PIN      GPIO_NUM_13  // 温度传感器
#define LIGHT_SENSOR_PIN     GPIO_NUM_14  // 光线传感器
```

### I2C扩展
推荐I2C引脚配置：
```c
#define I2C_SDA_PIN         GPIO_NUM_17   // I2C数据线
#define I2C_SCL_PIN         GPIO_NUM_18   // I2C时钟线
```

### 额外PWM输出
可用于LED灯带或额外舵机：
```c
#define LED_STRIP_PIN       GPIO_NUM_22   // LED灯带
#define EXTRA_SERVO_PIN     GPIO_NUM_23   // 额外舵机
```

## 电气特性

### 电压等级
- **逻辑电平**: 3.3V
- **输入容忍**: 5V (部分引脚)
- **输出电流**: 最大40mA每引脚
- **总电流**: 最大200mA所有引脚

### 上拉/下拉
- **内置上拉**: 45kΩ (可配置)
- **内置下拉**: 45kΩ (可配置)
- **外部电阻**: 建议10kΩ用于关键信号

### 驱动能力
- **标准引脚**: 20mA推荐，40mA最大
- **高驱动引脚**: 40mA推荐，80mA最大
- **开漏输出**: 支持，适合I2C等应用

## 布线建议

### 信号完整性
1. **高速信号**: SPI信号线尽量短，避免长走线
2. **时钟信号**: 差分布线或地线保护
3. **电源去耦**: 每个芯片附近放置去耦电容
4. **接地**: 良好的接地平面设计

### EMI/EMC
1. **屏蔽**: 高频信号使用屏蔽线
2. **滤波**: 电源线添加滤波电容
3. **隔离**: 数字和模拟信号分离布线
4. **阻抗匹配**: 高速信号线阻抗控制

### 机械考虑
1. **连接器**: 使用可靠的连接器
2. **应力释放**: 线缆添加应力释放
3. **固定**: PCB和连接器适当固定
4. **维护**: 预留调试和维护空间
