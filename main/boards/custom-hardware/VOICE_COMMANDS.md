# 语音控制命令参考

## 概述

本文档列出了xiaozhi-esp32自定义硬件系统支持的所有语音控制命令。通过MCP协议，用户可以使用自然语言控制双屏眼睛表情系统和舵机控制系统。

## 眼睛表情控制命令

### 基础表情设置

#### `self.eyes.set_emotion`
设置眼睛的表情状态

**参数:**
- `emotion` (string): 表情类型

**支持的表情:**
- `"normal"` - 正常状态
- `"happy"` - 开心表情
- `"angry"` - 生气表情  
- `"surprised"` - 惊讶表情
- `"sleepy"` - 困倦表情

**语音示例:**
```
"表现开心" → emotion="happy"
"生气的表情" → emotion="angry"  
"看起来很惊讶" → emotion="surprised"
"困倦的样子" → emotion="sleepy"
"恢复正常" → emotion="normal"
```

### 眼部动作控制

#### `self.eyes.blink`
执行眨眼动作

**参数:** 无

**语音示例:**
```
"眨眨眼"
"眨一下眼睛"
"眨眼"
```

#### `self.eyes.look_at`
控制眼球移动方向

**参数:**
- `x` (integer): 水平方向 (-15 到 15)
- `y` (integer): 垂直方向 (-15 到 15)

**方向说明:**
- x > 0: 向右看
- x < 0: 向左看  
- y > 0: 向下看
- y < 0: 向上看
- x=0, y=0: 看正前方

**语音示例:**
```
"向左看" → x=-10, y=0
"向右看" → x=10, y=0
"向上看" → x=0, y=-10
"向下看" → x=0, y=10
"看正前方" → x=0, y=0
```

### 特效控制

#### `self.eyes.heartbeat`
创建心跳效果

**参数:** 无

**语音示例:**
```
"心跳效果"
"瞳孔跳动"
"心跳"
```

#### `self.eyes.sparkle`
创建闪烁特效

**参数:** 无

**语音示例:**
```
"闪烁效果"
"眼睛闪烁"
"闪一下"
```

## 舵机控制命令

### 基础角度控制

#### `self.servo.set_angle`
设置舵机角度

**参数:**
- `target` (string): 控制目标
- `angle` (integer): 角度值 (0-180度)

**目标选择:**
- `"left"` / `"left_arm"` / `"left_hand"` - 左臂
- `"right"` / `"right_arm"` / `"right_hand"` - 右臂
- `"both"` / `"both_arms"` / `"both_hands"` - 双臂

**语音示例:**
```
"左手举到90度" → target="left", angle=90
"右手放下" → target="right", angle=0
"双手举起" → target="both", angle=180
"左臂到中间位置" → target="left", angle=90
```

### 动作手势

#### `self.servo.wave`
执行挥手动作

**参数:**
- `target` (string): 控制目标 (可选，默认"both")

**语音示例:**
```
"挥挥手" → target="both"
"左手挥手" → target="left"
"右手挥一下" → target="right"
```

#### `self.servo.reset`
重置到中性位置

**参数:**
- `target` (string): 控制目标 (可选，默认"both")

**语音示例:**
```
"重置手臂" → target="both"
"左手复位" → target="left"
"回到初始位置" → target="both"
```

### 预设动作

#### `self.servo.greeting`
执行问候手势

**参数:** 无

**语音示例:**
```
"打招呼"
"问候手势"
"做个问候"
```

#### `self.servo.goodbye`
执行告别手势

**参数:** 无

**语音示例:**
```
"再见手势"
"告别"
"挥手告别"
```

#### `self.servo.celebration`
执行庆祝手势

**参数:** 无

**语音示例:**
```
"庆祝一下"
"庆祝手势"
"欢呼"
```

#### `self.servo.thinking`
执行思考手势

**参数:** 无

**语音示例:**
```
"思考的样子"
"做思考手势"
"思考状态"
```

## 组合控制示例

### 情感表达组合
```
用户: "表现得很开心"
系统执行:
1. self.eyes.set_emotion(emotion="happy")
2. self.servo.celebration()

用户: "看起来很困"  
系统执行:
1. self.eyes.set_emotion(emotion="sleepy")
2. self.servo.reset(target="both")
```

### 交互场景组合
```
用户: "打个招呼"
系统执行:
1. self.eyes.set_emotion(emotion="happy")
2. self.servo.greeting()
3. self.eyes.blink()

用户: "告别"
系统执行:
1. self.servo.goodbye()
2. self.eyes.look_at(x=0, y=0)
```

## 自然语言映射

### 常用表达映射表

| 用户表达 | 对应命令 | 参数 |
|---------|---------|------|
| "开心点" | self.eyes.set_emotion | emotion="happy" |
| "生气" | self.eyes.set_emotion | emotion="angry" |
| "举起左手" | self.servo.set_angle | target="left", angle=180 |
| "放下右手" | self.servo.set_angle | target="right", angle=0 |
| "看左边" | self.eyes.look_at | x=-10, y=0 |
| "看右边" | self.eyes.look_at | x=10, y=0 |
| "挥手" | self.servo.wave | target="both" |
| "眨眼" | self.eyes.blink | - |

### 语音识别优化建议

1. **清晰发音**: 确保关键词发音清晰
2. **标准用词**: 使用文档中列出的标准表达
3. **简洁指令**: 避免过于复杂的句子结构
4. **等待响应**: 等待上一个动作完成再发出下一个指令

## 系统状态反馈

### 执行确认
系统执行命令后会通过以下方式提供反馈：
- **日志输出**: 控制台显示执行状态
- **视觉反馈**: 眼睛和手臂的实际动作
- **语音反馈**: 可选的语音确认 (如果配置)

### 错误处理
- **无效参数**: 系统会忽略无效的参数值
- **硬件故障**: 显示错误日志并尝试恢复
- **冲突检测**: 避免同时执行冲突的动作

## 扩展开发

### 添加新命令
1. 在对应的控制器中添加MCP工具注册
2. 实现命令处理逻辑
3. 更新本文档

### 自定义动作序列
```cpp
// 示例：添加自定义打招呼动作
mcp_server.AddTool("self.custom.my_greeting",
    "Custom greeting gesture",
    PropertyList({}),
    [](const PropertyList& properties) -> ReturnValue {
        eyes_set_emotion(EYE_STATE_HAPPY);
        servo_ctrl.SetAngle("right", 135);
        vTaskDelay(pdMS_TO_TICKS(500));
        servo_ctrl.Wave("right");
        return true;
    });
```

### 语音识别集成
系统通过以下流程处理语音命令：
1. **语音识别**: 将语音转换为文本
2. **意图理解**: AI解析用户意图
3. **命令映射**: 将意图映射到MCP命令
4. **参数提取**: 提取命令参数
5. **执行反馈**: 执行命令并提供反馈

这种设计确保了自然语言与硬件控制的无缝集成。
