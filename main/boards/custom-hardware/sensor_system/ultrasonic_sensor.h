#pragma once

#include <driver/gpio.h>
#include <esp_timer.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include <functional>

/**
 * @brief 超声波传感器类 (HC-SR04)
 * 
 * 支持距离测量和障碍物检测
 * 测量范围: 2cm - 400cm
 * 精度: 3mm
 */
class UltrasonicSensor {
public:
    /**
     * @brief 构造函数
     * @param trig_pin 触发引脚
     * @param echo_pin 回声引脚
     */
    UltrasonicSensor(gpio_num_t trig_pin, gpio_num_t echo_pin);
    
    /**
     * @brief 析构函数
     */
    ~UltrasonicSensor();
    
    /**
     * @brief 初始化传感器
     * @return true 初始化成功
     * @return false 初始化失败
     */
    bool Initialize();
    
    /**
     * @brief 测量距离 (单次测量)
     * @return 距离值 (cm)，-1表示测量失败
     */
    float MeasureDistance();
    
    /**
     * @brief 开始连续测量
     * @param interval_ms 测量间隔 (毫秒)
     * @param callback 距离变化回调函数
     */
    void StartContinuousMeasurement(uint32_t interval_ms, std::function<void(float)> callback);
    
    /**
     * @brief 停止连续测量
     */
    void StopContinuousMeasurement();
    
    /**
     * @brief 设置障碍物检测阈值
     * @param threshold_cm 阈值距离 (cm)
     * @param callback 障碍物检测回调
     */
    void SetObstacleDetection(float threshold_cm, std::function<void(bool)> callback);
    
    /**
     * @brief 获取最后一次测量的距离
     * @return 距离值 (cm)
     */
    float GetLastDistance() const { return last_distance_; }
    
    /**
     * @brief 检查传感器是否正常工作
     * @return true 正常工作
     * @return false 故障
     */
    bool IsWorking() const { return is_working_; }

private:
    gpio_num_t trig_pin_;
    gpio_num_t echo_pin_;
    esp_timer_handle_t measurement_timer_;
    TaskHandle_t measurement_task_handle_;
    
    float last_distance_;
    float obstacle_threshold_;
    bool is_working_;
    bool is_continuous_measuring_;
    bool obstacle_detected_;
    
    std::function<void(float)> distance_callback_;
    std::function<void(bool)> obstacle_callback_;
    
    /**
     * @brief 发送触发脉冲
     */
    void SendTriggerPulse();
    
    /**
     * @brief 等待回声并计算距离
     * @return 距离值 (cm)
     */
    float WaitForEcho();
    
    /**
     * @brief 连续测量任务
     */
    static void MeasurementTask(void* parameter);
    
    /**
     * @brief 定时器回调
     */
    static void TimerCallback(void* arg);
};
