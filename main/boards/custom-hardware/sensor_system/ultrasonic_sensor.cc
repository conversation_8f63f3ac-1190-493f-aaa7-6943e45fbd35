#include "ultrasonic_sensor.h"
#include <esp_log.h>
#include <rom/ets_sys.h>

static const char* TAG = "UltrasonicSensor";

// 声速常量 (cm/us)
static const float SOUND_SPEED = 0.034;
static const uint32_t TIMEOUT_US = 30000; // 30ms超时

UltrasonicSensor::UltrasonicSensor(gpio_num_t trig_pin, gpio_num_t echo_pin)
    : trig_pin_(trig_pin)
    , echo_pin_(echo_pin)
    , measurement_timer_(nullptr)
    , measurement_task_handle_(nullptr)
    , last_distance_(-1.0f)
    , obstacle_threshold_(30.0f)
    , is_working_(false)
    , is_continuous_measuring_(false)
    , obstacle_detected_(false)
{
}

UltrasonicSensor::~UltrasonicSensor() {
    StopContinuousMeasurement();
    if (measurement_timer_) {
        esp_timer_delete(measurement_timer_);
    }
}

bool UltrasonicSensor::Initialize() {
    // 配置触发引脚为输出
    gpio_config_t trig_config = {
        .pin_bit_mask = (1ULL << trig_pin_),
        .mode = GPIO_MODE_OUTPUT,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .intr_type = GPIO_INTR_DISABLE
    };
    
    if (gpio_config(&trig_config) != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure trigger pin %d", trig_pin_);
        return false;
    }
    
    // 配置回声引脚为输入
    gpio_config_t echo_config = {
        .pin_bit_mask = (1ULL << echo_pin_),
        .mode = GPIO_MODE_INPUT,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .intr_type = GPIO_INTR_DISABLE
    };
    
    if (gpio_config(&echo_config) != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure echo pin %d", echo_pin_);
        return false;
    }
    
    // 初始化触发引脚为低电平
    gpio_set_level(trig_pin_, 0);
    
    // 测试传感器是否工作正常
    float test_distance = MeasureDistance();
    is_working_ = (test_distance > 0);
    
    if (is_working_) {
        ESP_LOGI(TAG, "Ultrasonic sensor initialized successfully on pins TRIG:%d ECHO:%d", 
                 trig_pin_, echo_pin_);
    } else {
        ESP_LOGW(TAG, "Ultrasonic sensor may not be working properly");
    }
    
    return true;
}

float UltrasonicSensor::MeasureDistance() {
    SendTriggerPulse();
    float distance = WaitForEcho();
    
    if (distance > 0) {
        last_distance_ = distance;
        
        // 检查障碍物
        if (obstacle_callback_) {
            bool current_obstacle = (distance < obstacle_threshold_);
            if (current_obstacle != obstacle_detected_) {
                obstacle_detected_ = current_obstacle;
                obstacle_callback_(obstacle_detected_);
            }
        }
    }
    
    return distance;
}

void UltrasonicSensor::SendTriggerPulse() {
    gpio_set_level(trig_pin_, 0);
    ets_delay_us(2);
    gpio_set_level(trig_pin_, 1);
    ets_delay_us(10);
    gpio_set_level(trig_pin_, 0);
}

float UltrasonicSensor::WaitForEcho() {
    uint32_t start_time = 0;
    uint32_t end_time = 0;
    uint32_t timeout_start = esp_timer_get_time();
    
    // 等待回声引脚变高
    while (gpio_get_level(echo_pin_) == 0) {
        if ((esp_timer_get_time() - timeout_start) > TIMEOUT_US) {
            ESP_LOGW(TAG, "Timeout waiting for echo start");
            return -1.0f;
        }
    }
    start_time = esp_timer_get_time();
    
    // 等待回声引脚变低
    while (gpio_get_level(echo_pin_) == 1) {
        if ((esp_timer_get_time() - start_time) > TIMEOUT_US) {
            ESP_LOGW(TAG, "Timeout waiting for echo end");
            return -1.0f;
        }
    }
    end_time = esp_timer_get_time();
    
    // 计算距离
    uint32_t duration_us = end_time - start_time;
    float distance_cm = (duration_us * SOUND_SPEED) / 2.0f;
    
    // 有效范围检查
    if (distance_cm < 2.0f || distance_cm > 400.0f) {
        return -1.0f;
    }
    
    return distance_cm;
}

void UltrasonicSensor::StartContinuousMeasurement(uint32_t interval_ms, std::function<void(float)> callback) {
    if (is_continuous_measuring_) {
        StopContinuousMeasurement();
    }
    
    distance_callback_ = callback;
    is_continuous_measuring_ = true;
    
    // 创建定时器
    esp_timer_create_args_t timer_args = {
        .callback = TimerCallback,
        .arg = this,
        .name = "ultrasonic_timer"
    };
    
    if (esp_timer_create(&timer_args, &measurement_timer_) == ESP_OK) {
        esp_timer_start_periodic(measurement_timer_, interval_ms * 1000);
        ESP_LOGI(TAG, "Started continuous measurement with %dms interval", interval_ms);
    } else {
        ESP_LOGE(TAG, "Failed to create measurement timer");
        is_continuous_measuring_ = false;
    }
}

void UltrasonicSensor::StopContinuousMeasurement() {
    if (measurement_timer_) {
        esp_timer_stop(measurement_timer_);
        esp_timer_delete(measurement_timer_);
        measurement_timer_ = nullptr;
    }
    
    is_continuous_measuring_ = false;
    distance_callback_ = nullptr;
    
    ESP_LOGI(TAG, "Stopped continuous measurement");
}

void UltrasonicSensor::SetObstacleDetection(float threshold_cm, std::function<void(bool)> callback) {
    obstacle_threshold_ = threshold_cm;
    obstacle_callback_ = callback;
    ESP_LOGI(TAG, "Set obstacle detection threshold to %.1f cm", threshold_cm);
}

void UltrasonicSensor::TimerCallback(void* arg) {
    UltrasonicSensor* sensor = static_cast<UltrasonicSensor*>(arg);
    float distance = sensor->MeasureDistance();
    
    if (distance > 0 && sensor->distance_callback_) {
        sensor->distance_callback_(distance);
    }
}
