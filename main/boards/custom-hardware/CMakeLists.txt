# 舵机控制系统 - 支持180度舵机和MCP协议控制
set(MOTION_SYSTEM_SRCS
    motion_system/servo_180.cc
    motion_system/servo_mcp.cc
    motion_system/servo_degree_mapper.cc
)

# 双屏眼睛表情系统 - 基于双GC9A01显示屏的LVGL实现
set(EYES_SRCS
    eyes/eyes_controller.cc
)

idf_component_register(COMPONENT_NAME custom-hardware
    SRCS "custom_hardware.cc"
         ${MOTION_SYSTEM_SRCS}
         ${EYES_SRCS}
    INCLUDE_DIRS "."
                 "motion_system"
                 "eyes"
    REQUIRES esp_lcd lvgl
)