set(MOTION_SYSTEM_SRCS
    motion_system/servo_180.cc
    motion_system/servo_mcp.cc
    motion_system/servo_degree_mapper.cc
)

set(EYES_SRCS
    eyes/eyes_controller.cc
)

set(SENSOR_SYSTEM_SRCS
    sensor_system/ultrasonic_sensor.cc
    sensor_system/temperature_sensor.cc
    sensor_system/light_sensor.cc
    sensor_system/touch_sensor.cc
)

set(HARDWARE_LAYER_SRCS
    hardware_layer/pin_manager.cc
    hardware_layer/i2c_manager.cc
    hardware_layer/spi_manager.cc
    hardware_layer/gpio_manager.cc
)

set(EXPRESSION_SYSTEM_SRCS
    expression_system/emotion_controller.cc
    expression_system/animation_manager.cc
    expression_system/voice_response.cc
)

idf_component_register(COMPONENT_NAME custom-hardware
    SRCS "custom_hardware.cc"
         ${MOTION_SYSTEM_SRCS}
         ${EYES_SRCS}
         ${SENSOR_SYSTEM_SRCS}
         ${HARDWARE_LAYER_SRCS}
         ${EXPRESSION_SYSTEM_SRCS}
    INCLUDE_DIRS "."
                 "motion_system"
                 "eyes"
                 "sensor_system"
                 "hardware_layer"
                 "expression_system"
    REQUIRES esp_lcd lvgl esp_timer driver esp_adc
)