# Custom Hardware - 双屏眼睛表情系统 + 舵机控制系统

## 概述

本项目实现了基于ESP32-S3的智能机器人硬件系统，主要包含：
- **双屏眼睛表情系统**: 使用双GC9A01圆形显示屏实现生动的眼睛表情
- **舵机控制系统**: 支持双臂舵机控制，可执行各种手势动作
- **语音控制集成**: 通过MCP协议实现语音控制硬件设备

## 硬件配置

### ESP32-S3 引脚分配

#### 双屏眼睛表情系统 (双GC9A01)
```
SPI总线 (共享):
├── SCK  (时钟)     → GPIO 19
├── MOSI (数据输出) → GPIO 20  
├── DC   (数据/命令) → GPIO 21
└── RST  (复位)     → GPIO 1

片选引脚 (独立):
├── 左屏 CS → GPIO 2
└── 右屏 CS → GPIO 45
```

#### 舵机控制系统
```
MCPWM控制:
├── 左臂舵机 → GPIO 9
└── 右臂舵机 → GPIO 10
```

#### 其他系统引脚
```
基础功能:
├── 内置LED    → GPIO 48
├── 启动按钮   → GPIO 0
└── 测试灯控制 → GPIO 18

音频系统 (Simplex I2S):
├── 麦克风:
│   ├── WS  → GPIO 4
│   ├── SCK → GPIO 5
│   └── DIN → GPIO 6
└── 扬声器:
    ├── DOUT → GPIO 7
    ├── BCLK → GPIO 15
    └── LRCK → GPIO 16
```

## 功能特性

### 双屏眼睛表情系统

#### 支持的表情状态
- `EYE_STATE_NORMAL` - 正常状态
- `EYE_STATE_BLINKING` - 眨眼状态  
- `EYE_STATE_LOOKING` - 眼球移动状态
- `EYE_STATE_HAPPY` - 开心状态
- `EYE_STATE_ANGRY` - 生气状态
- `EYE_STATE_SLEEPY` - 困倦状态
- `EYE_STATE_SURPRISED` - 惊讶状态

#### 自动动画功能
- **自动眨眼**: 每3-6秒自然眨眼
- **随机眼球移动**: 每3-7秒平滑移动
- **情绪变化**: 每10-20秒随机切换表情
- **心跳效果**: 每20秒瞳孔律动
- **特殊动画序列**: 每30秒执行复杂动作组合

#### 控制接口
```c
// 基础控制
void eyes_set_emotion(eye_state_t emotion);
void eyes_blink(void);
void eyes_look_at(int32_t x, int32_t y);
void eyes_heartbeat(void);
void eyes_sparkle(void);
```

### 舵机控制系统

#### 基础控制
- **角度控制**: 0-180度精确控制
- **目标选择**: 支持"left"、"right"、"both"
- **平滑运动**: MCPWM硬件PWM控制

#### 预设动作
- `PerformGreeting()` - 问候手势
- `PerformGoodbye()` - 告别手势  
- `PerformCelebration()` - 庆祝手势
- `PerformThinking()` - 思考手势
- `Wave()` - 挥手动作

#### MCP语音控制命令
```json
{
  "self.servo.set_angle": {
    "target": "left|right|both",
    "angle": 0-180
  },
  "self.servo.wave": {
    "target": "left|right|both"
  },
  "self.servo.greeting": {},
  "self.servo.goodbye": {},
  "self.servo.celebration": {},
  "self.servo.thinking": {},
  "self.servo.reset": {
    "target": "left|right|both"
  }
}
```

## 技术实现

### 双屏系统架构
- **LVGL图形库**: 提供高性能UI渲染
- **双缓冲机制**: 确保流畅的动画效果
- **互斥锁保护**: 防止多线程访问冲突
- **引脚共享优化**: SPI总线共享，独立片选控制

### 舵机控制架构  
- **MCPWM硬件PWM**: 高精度角度控制
- **共享定时器**: 多舵机同步控制
- **角度映射**: 自动角度范围转换
- **动作序列**: 支持复杂动作编程

### 引脚冲突管理
- **启动时检查**: 自动检测引脚配置冲突
- **配置集中管理**: 所有引脚定义在config.h中
- **兼容性保证**: 向后兼容现有显示代码

## 使用示例

### 语音控制示例
```
用户: "举起右手"
系统: 调用 self.servo.set_angle(target="right", angle=180)

用户: "挥挥手"  
系统: 调用 self.servo.wave(target="both")

用户: "表现开心"
系统: 调用 eyes_set_emotion(EYE_STATE_HAPPY)
```

### 编程控制示例
```cpp
// 初始化系统
init_eyes_display();
ServoMcpController servo_ctrl(SERVO_LEFT_ARM_PIN, SERVO_RIGHT_ARM_PIN);

// 控制眼睛表情
eyes_set_emotion(EYE_STATE_HAPPY);
eyes_look_at(10, 0);  // 向右看

// 控制舵机动作
servo_ctrl.PerformGreeting();
servo_ctrl.SetAngle("both", 90);
```

## 故障排除

### 常见问题
1. **双屏显示异常**: 检查SPI引脚连接和片选引脚配置
2. **舵机不响应**: 确认MCPWM引脚配置和电源供应
3. **引脚冲突**: 查看启动日志中的引脚配置检查信息

### 调试信息
系统启动时会输出详细的引脚配置信息：
```
🔍 检查引脚配置...
   SPI引脚: SCK=19, MOSI=20, DC=21, RST=1
   片选引脚: LEFT_CS=2, RIGHT_CS=45  
   舵机引脚: LEFT_ARM=9, RIGHT_ARM=10
```

## 扩展开发

### 添加新表情
1. 在`eye_state_t`枚举中添加新状态
2. 在`set_eye_emotion()`中添加对应参数
3. 可选：添加专用动画函数

### 添加新动作
1. 在`ServoMcpController`中添加新的预设函数
2. 在MCP工具注册中添加对应命令
3. 定义动作序列和时间参数

### 引脚重新分配
1. 修改`config.h`中的引脚定义
2. 确保无引脚冲突
3. 重新编译和测试
