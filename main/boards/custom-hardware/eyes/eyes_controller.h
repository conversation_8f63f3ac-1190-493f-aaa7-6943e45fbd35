#pragma once

#include "lvgl.h"
#include "esp_lcd_panel_ops.h"
#include "esp_lcd_panel_io.h"

#ifdef __cplusplus
extern "C" {
#endif

// 眼睛状态枚举
typedef enum {
    EYE_STATE_NORMAL,      // 正常状态
    EYE_STATE_BLINKING,    // 眨眼状态
    EYE_STATE_LOOKING,     // 眼球移动状态
    EYE_STATE_HAPPY,       // 开心状态
    EYE_STATE_ANGRY,       // 生气状态
    EYE_STATE_SLEEPY,      // 困倦状态
    EYE_STATE_SURPRISED    // 惊讶状态
} eye_state_t;

// 眼睛结构体
typedef struct {
    lv_obj_t *eye_container;    // 眼球容器
    lv_obj_t *pupil;           // 瞳孔
    lv_obj_t *highlight;       // 高光点
    lv_obj_t *eyelid_top;      // 上眼睑
    lv_obj_t *eyelid_bottom;   // 下眼睑
    eye_state_t state;         // 当前状态
    int32_t pupil_x;           // 瞳孔X位置
    int32_t pupil_y;           // 瞳孔Y位置
    int32_t start_x;           // 动画起始X位置
    int32_t start_y;           // 动画起始Y位置
    int32_t target_x;          // 动画目标X位置
    int32_t target_y;          // 动画目标Y位置
    bool is_left_eye;          // 是否为左眼
} eye_t;

// 初始化眼睛显示系统
void init_eyes_display(void);

// 控制眼睛动画的函数
void eyes_set_emotion(eye_state_t emotion);
void eyes_blink(void);
void eyes_look_at(int32_t x, int32_t y);
void eyes_heartbeat(void);
void eyes_sparkle(void);

#ifdef __cplusplus
}
#endif
