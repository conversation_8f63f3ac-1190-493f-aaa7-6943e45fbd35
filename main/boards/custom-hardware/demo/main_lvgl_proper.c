/**
 * @file main_lvgl_proper.c
 * @brief ESP32双眼显示系统 - 基于lvgl_demo.c的正确LVGL实现
 * 
 * 基于lvgl_demo.c的架构，适配双GC9A01显示屏的LVGL实现
 * 使用官方GC9A01宏配置、LVGL任务管理、定时器驱动等最佳实践
 */

#include <stdio.h>
#include <inttypes.h>
#include "freertos/FreeRTOS.h"
#include "freertos/semphr.h"
#include "freertos/task.h"
#include "esp_timer.h"
#include "esp_lcd_panel_io.h"
#include "esp_lcd_panel_vendor.h"
#include "esp_lcd_panel_ops.h"
#include "driver/gpio.h"
#include "driver/spi_master.h"
#include "esp_err.h"
#include "esp_log.h"
#include "esp_random.h"
#include "lvgl.h"
#include "esp_lcd_gc9a01.h"

// 使用SPI2总线
#define LCD_HOST SPI2_HOST

// 屏幕尺寸
#define SCREEN_WIDTH  240
#define SCREEN_HEIGHT 240

// 硬件引脚配置（基于用户的硬件配置）
#define PIN_SCK       19    // SPI时钟
#define PIN_MOSI      20    // SPI数据输出
#define PIN_DC        21    // 数据/命令控制
#define PIN_RST       1     // 复位引脚（共享）
#define PIN_CS_LEFT   2     // 左屏片选
#define PIN_CS_RIGHT  45    // 右屏片选

// LVGL配置
#define LVGL_TICK_PERIOD_MS     2
#define LVGL_TASK_MAX_DELAY_MS  500
#define LVGL_TASK_MIN_DELAY_MS  1
#define LVGL_TASK_STACK_SIZE    (6 * 1024)
#define LVGL_TASK_PRIORITY      2
#define LVGL_BUFFER_HEIGHT      60    // 缓冲区高度（行数）

static const char *TAG = "LVGL_PROPER";

// 眼睛状态枚举
typedef enum {
    EYE_STATE_NORMAL,      // 正常状态
    EYE_STATE_BLINKING,    // 眨眼状态
    EYE_STATE_LOOKING,     // 眼球移动状态
    EYE_STATE_HAPPY,       // 开心状态
    EYE_STATE_ANGRY,       // 生气状态
    EYE_STATE_SLEEPY,      // 困倦状态
    EYE_STATE_SURPRISED    // 惊讶状态
} eye_state_t;

// 眼睛结构体
typedef struct {
    lv_obj_t *eye_container;    // 眼球容器
    lv_obj_t *pupil;           // 瞳孔
    lv_obj_t *highlight;       // 高光点
    lv_obj_t *eyelid_top;      // 上眼睑
    lv_obj_t *eyelid_bottom;   // 下眼睑
    lv_anim_t blink_anim;      // 眨眼动画
    lv_anim_t look_anim;       // 眼球移动动画
    eye_state_t state;         // 当前状态
    int32_t pupil_x;           // 瞳孔X位置
    int32_t pupil_y;           // 瞳孔Y位置
    // 动画状态跟踪 - 解决抖动问题
    int32_t start_x;           // 动画起始X位置
    int32_t start_y;           // 动画起始Y位置
    int32_t target_x;          // 动画目标X位置
    int32_t target_y;          // 动画目标Y位置
    bool is_left_eye;          // 是否为左眼
} eye_t;

// 函数前向声明
static void start_blink_animation(eye_t *eye);
static void start_look_animation(eye_t *eye, int32_t target_x, int32_t target_y);
static void set_eye_emotion(eye_t *eye, eye_state_t emotion);
static void create_heartbeat_effect(eye_t *eye);
static void create_sparkle_effect(eye_t *eye);

// 全局变量
static SemaphoreHandle_t lvgl_mux = NULL;
static esp_lcd_panel_handle_t left_panel_handle = NULL;
static esp_lcd_panel_handle_t right_panel_handle = NULL;
static lv_disp_t *left_disp = NULL;
static lv_disp_t *right_disp = NULL;
static eye_t left_eye = {0};
static eye_t right_eye = {0};
static lv_timer_t *eye_animation_timer = NULL;

// LVGL刷新完成回调 - 左屏
static bool notify_left_flush_ready(esp_lcd_panel_io_handle_t panel_io, esp_lcd_panel_io_event_data_t *edata, void *user_ctx)
{
    lv_disp_drv_t *disp_driver = (lv_disp_drv_t *)user_ctx;
    lv_disp_flush_ready(disp_driver);
    return false;
}

// LVGL刷新完成回调 - 右屏
static bool notify_right_flush_ready(esp_lcd_panel_io_handle_t panel_io, esp_lcd_panel_io_event_data_t *edata, void *user_ctx)
{
    lv_disp_drv_t *disp_driver = (lv_disp_drv_t *)user_ctx;
    lv_disp_flush_ready(disp_driver);
    return false;
}

// LVGL刷新回调 - 左屏
static void left_flush_cb(lv_disp_drv_t *drv, const lv_area_t *area, lv_color_t *color_map)
{
    esp_lcd_panel_handle_t panel_handle = (esp_lcd_panel_handle_t)drv->user_data;
    esp_lcd_panel_draw_bitmap(panel_handle, area->x1, area->y1, area->x2 + 1, area->y2 + 1, color_map);
}

// LVGL刷新回调 - 右屏
static void right_flush_cb(lv_disp_drv_t *drv, const lv_area_t *area, lv_color_t *color_map)
{
    esp_lcd_panel_handle_t panel_handle = (esp_lcd_panel_handle_t)drv->user_data;
    esp_lcd_panel_draw_bitmap(panel_handle, area->x1, area->y1, area->x2 + 1, area->y2 + 1, color_map);
}

// LVGL定时器回调
static void increase_lvgl_tick(void *arg)
{
    lv_tick_inc(LVGL_TICK_PERIOD_MS);
}

// LVGL互斥锁操作
static bool lvgl_lock(int timeout_ms)
{
    assert(lvgl_mux && "LVGL mutex must be initialized first");
    const TickType_t timeout_ticks = (timeout_ms == -1) ? portMAX_DELAY : pdMS_TO_TICKS(timeout_ms);
    return xSemaphoreTake(lvgl_mux, timeout_ticks) == pdTRUE;
}

static void lvgl_unlock(void)
{
    assert(lvgl_mux && "LVGL mutex must be initialized first");
    xSemaphoreGive(lvgl_mux);
}

// 初始化单个GC9A01屏幕
static esp_err_t init_gc9a01_panel(int cs_pin, esp_lcd_panel_handle_t *panel_handle, 
                                   lv_disp_drv_t *disp_drv, bool (*flush_ready_cb)(esp_lcd_panel_io_handle_t, esp_lcd_panel_io_event_data_t*, void*),
                                   void (*flush_cb)(lv_disp_drv_t*, const lv_area_t*, lv_color_t*))
{
    ESP_LOGI(TAG, "🔧 初始化GC9A01屏幕 (CS引脚: %d)", cs_pin);
    
    // 创建面板IO
    esp_lcd_panel_io_handle_t io_handle = NULL;
    const esp_lcd_panel_io_spi_config_t io_config = GC9A01_PANEL_IO_SPI_CONFIG(cs_pin, PIN_DC, flush_ready_cb, disp_drv);
    ESP_ERROR_CHECK(esp_lcd_new_panel_io_spi((esp_lcd_spi_bus_handle_t)LCD_HOST, &io_config, &io_handle));
    
    // 创建面板设备
    const esp_lcd_panel_dev_config_t panel_config = {
        .reset_gpio_num = PIN_RST,
        .rgb_ele_order = LCD_RGB_ELEMENT_ORDER_RGB,  // 使用RGB顺序
        .data_endian = LCD_RGB_DATA_ENDIAN_BIG,      // 大端序
        .bits_per_pixel = 16,
    };
    ESP_ERROR_CHECK(esp_lcd_new_panel_gc9a01(io_handle, &panel_config, panel_handle));
    
    // 配置面板
    ESP_ERROR_CHECK(esp_lcd_panel_reset(*panel_handle));
    ESP_ERROR_CHECK(esp_lcd_panel_init(*panel_handle));
    ESP_ERROR_CHECK(esp_lcd_panel_invert_color(*panel_handle, true));  // 颜色反转
    ESP_ERROR_CHECK(esp_lcd_panel_disp_on_off(*panel_handle, true));
    
    // 初始化LVGL显示驱动
    lv_disp_drv_init(disp_drv);
    disp_drv->hor_res = SCREEN_WIDTH;
    disp_drv->ver_res = SCREEN_HEIGHT;
    disp_drv->flush_cb = flush_cb;
    disp_drv->user_data = *panel_handle;
    
    ESP_LOGI(TAG, "✅ GC9A01屏幕初始化完成 (CS引脚: %d)", cs_pin);
    return ESP_OK;
}

// 动画完成回调函数
static void blink_ready_cb(lv_anim_t *a)
{
    eye_t *eye = (eye_t *)lv_anim_get_user_data(a);
    eye->state = EYE_STATE_NORMAL;
}

static void look_ready_cb(lv_anim_t *a)
{
    eye_t *eye = (eye_t *)lv_anim_get_user_data(a);
    eye->state = EYE_STATE_NORMAL;
}

static void heartbeat_exec_cb(void *obj, int32_t value)
{
    lv_obj_set_size((lv_obj_t*)obj, value, value);
}

static void sparkle_exec_cb(void *obj, int32_t value)
{
    lv_obj_set_style_bg_opa((lv_obj_t*)obj, value, 0);
}

static void sparkle_ready_cb(lv_anim_t *a)
{
    lv_obj_t *sparkle = (lv_obj_t*)a->var;
    lv_obj_del(sparkle);
}

// 定时器回调函数
static void restore_emotion_cb(lv_timer_t *t)
{
    set_eye_emotion(&left_eye, EYE_STATE_NORMAL);
    set_eye_emotion(&right_eye, EYE_STATE_NORMAL);
    ESP_LOGI(TAG, "😐 恢复正常状态");
    lv_timer_del(t);
}

static void seq_look_left_cb(lv_timer_t *t)
{
    start_look_animation(&left_eye, -12, 0);
    start_look_animation(&right_eye, -12, 0);
    ESP_LOGI(TAG, "👈 平滑向左看");
    lv_timer_del(t);
}

static void seq_look_right_cb(lv_timer_t *t)
{
    start_look_animation(&left_eye, 12, 0);
    start_look_animation(&right_eye, 12, 0);
    ESP_LOGI(TAG, "👉 平滑向右看");
    lv_timer_del(t);
}

static void seq_look_up_cb(lv_timer_t *t)
{
    start_look_animation(&left_eye, 0, -10);
    start_look_animation(&right_eye, 0, -10);
    ESP_LOGI(TAG, "👆 平滑向上看");
    lv_timer_del(t);
}

static void seq_look_center_cb(lv_timer_t *t)
{
    start_look_animation(&left_eye, 0, 0);
    start_look_animation(&right_eye, 0, 0);
    ESP_LOGI(TAG, "🎯 回到中心");
    lv_timer_del(t);
}

static void seq_blink_cb(lv_timer_t *t)
{
    start_blink_animation(&left_eye);
    start_blink_animation(&right_eye);
    ESP_LOGI(TAG, "😉 序列结束眨眼");
    lv_timer_del(t);
}

static void super_angry_cb(lv_timer_t *t)
{
    set_eye_emotion(&left_eye, EYE_STATE_ANGRY);
    set_eye_emotion(&right_eye, EYE_STATE_ANGRY);
    ESP_LOGI(TAG, "😠 愤怒模式");
    lv_timer_del(t);
}

static void super_surprised_cb(lv_timer_t *t)
{
    set_eye_emotion(&left_eye, EYE_STATE_SURPRISED);
    set_eye_emotion(&right_eye, EYE_STATE_SURPRISED);
    ESP_LOGI(TAG, "😲 惊讶模式");
    lv_timer_del(t);
}

static void super_happy_cb(lv_timer_t *t)
{
    set_eye_emotion(&left_eye, EYE_STATE_HAPPY);
    set_eye_emotion(&right_eye, EYE_STATE_HAPPY);
    ESP_LOGI(TAG, "😊 开心模式");
    lv_timer_del(t);
}

static void super_normal_cb(lv_timer_t *t)
{
    set_eye_emotion(&left_eye, EYE_STATE_NORMAL);
    set_eye_emotion(&right_eye, EYE_STATE_NORMAL);
    ESP_LOGI(TAG, "😐 恢复正常");
    lv_timer_del(t);
}

// 上眼睑眨眼动画回调
static void blink_top_anim_cb(void *obj, int32_t value)
{
    lv_obj_t *eyelid_top = (lv_obj_t *)obj;
    lv_obj_set_height(eyelid_top, value);
}

// 下眼睑眨眼动画回调
static void blink_bottom_anim_cb(void *obj, int32_t value)
{
    lv_obj_t *eyelid_bottom = (lv_obj_t *)obj;
    lv_obj_set_height(eyelid_bottom, value);
}

// 统一的瞳孔移动动画回调 - 解决抖动问题
static void pupil_move_unified_cb(void *obj, int32_t value)
{
    eye_t *eye = (eye_t *)obj;

    // 计算当前动画进度 (0-1000)
    float progress = value / 1000.0f;

    // 使用插值计算当前位置
    int32_t current_x = eye->start_x + (eye->target_x - eye->start_x) * progress;
    int32_t current_y = eye->start_y + (eye->target_y - eye->start_y) * progress;

    // 计算瞳孔中心位置 - 动态适应瞳孔大小
    int32_t pupil_size = lv_obj_get_width(eye->pupil);
    int32_t center_x = (200 - pupil_size) / 2;
    int32_t center_y = (200 - pupil_size) / 2;

    // 设置瞳孔位置
    lv_obj_set_pos(eye->pupil, center_x + current_x, center_y + current_y);

    // 平滑更新高光位置 - 大光点在左下，反向移动更自然
    int32_t highlight_x = 30 - current_x/4;  // 反向移动，幅度更小
    int32_t highlight_y = 55 - current_y/4;  // 反向移动，幅度更小

    // 限制高光在瞳孔范围内
    highlight_x = LV_CLAMP(10, highlight_x, pupil_size - 15);
    highlight_y = LV_CLAMP(15, highlight_y, pupil_size - 10);

    lv_obj_set_pos(eye->highlight, highlight_x, highlight_y);

    // 更新当前位置记录
    eye->pupil_x = current_x;
    eye->pupil_y = current_y;
}

// 创建完整的眨眼动画 - 上下眼睑都闭合，解决冲突问题
static void start_blink_animation(eye_t *eye)
{
    if (eye->state == EYE_STATE_BLINKING) return;

    // 暂停所有瞳孔移动动画，避免冲突
    lv_anim_del(eye, pupil_move_unified_cb);

    eye->state = EYE_STATE_BLINKING;

    // 上眼睑下降动画
    lv_anim_t anim_top;
    lv_anim_init(&anim_top);
    lv_anim_set_var(&anim_top, eye->eyelid_top);
    lv_anim_set_values(&anim_top, 0, 100);  // 从0到100像素高度，完全闭合
    lv_anim_set_time(&anim_top, 120);       // 120ms下降
    lv_anim_set_exec_cb(&anim_top, blink_top_anim_cb);
    lv_anim_set_path_cb(&anim_top, lv_anim_path_ease_in);
    lv_anim_set_repeat_count(&anim_top, 1);
    lv_anim_set_playback_time(&anim_top, 180);  // 180ms上升
    lv_anim_set_ready_cb(&anim_top, blink_ready_cb);
    lv_anim_set_user_data(&anim_top, eye);
    lv_anim_start(&anim_top);

    // 下眼睑上升动画 - 同时进行
    lv_anim_t anim_bottom;
    lv_anim_init(&anim_bottom);
    lv_anim_set_var(&anim_bottom, eye->eyelid_bottom);
    lv_anim_set_values(&anim_bottom, 0, 100);  // 从0到100像素高度，完全闭合
    lv_anim_set_time(&anim_bottom, 120);       // 120ms上升
    lv_anim_set_exec_cb(&anim_bottom, blink_bottom_anim_cb);
    lv_anim_set_path_cb(&anim_bottom, lv_anim_path_ease_in);
    lv_anim_set_repeat_count(&anim_bottom, 1);
    lv_anim_set_playback_time(&anim_bottom, 180);  // 180ms下降
    lv_anim_start(&anim_bottom);
}

// 创建真正平滑的眼球移动动画 - 统一处理XY轴
static void start_look_animation(eye_t *eye, int32_t target_x, int32_t target_y)
{
    if (eye->state == EYE_STATE_BLINKING) return;

    eye->state = EYE_STATE_LOOKING;

    // 限制移动范围
    target_x = LV_CLAMP(-15, target_x, 15);
    target_y = LV_CLAMP(-15, target_y, 15);

    // 停止之前的动画
    lv_anim_del(eye, pupil_move_unified_cb);

    // 设置动画起始和目标位置
    eye->start_x = eye->pupil_x;
    eye->start_y = eye->pupil_y;
    eye->target_x = target_x;
    eye->target_y = target_y;

    // 创建统一的移动动画
    lv_anim_t anim_unified;
    lv_anim_init(&anim_unified);
    lv_anim_set_var(&anim_unified, eye);
    lv_anim_set_values(&anim_unified, 0, 1000);  // 使用0-1000作为进度值
    lv_anim_set_time(&anim_unified, 800);  // 800ms更平滑的移动
    lv_anim_set_exec_cb(&anim_unified, pupil_move_unified_cb);
    lv_anim_set_path_cb(&anim_unified, lv_anim_path_ease_in_out);
    lv_anim_set_ready_cb(&anim_unified, look_ready_cb);
    lv_anim_set_user_data(&anim_unified, eye);
    lv_anim_start(&anim_unified);

    // 更新瞳孔位置记录
    eye->pupil_x = target_x;
    eye->pupil_y = target_y;
}

// 平滑表情切换动画回调 - 保持相对位置
static void emotion_pupil_size_cb(void *obj, int32_t value)
{
    lv_obj_t *pupil = (lv_obj_t *)obj;

    // 获取当前瞳孔位置
    lv_coord_t current_x = lv_obj_get_x(pupil);
    lv_coord_t current_y = lv_obj_get_y(pupil);

    // 计算当前相对于中心的偏移量
    int32_t old_size = lv_obj_get_width(pupil);
    int32_t old_center = (200 - old_size) / 2;
    int32_t offset_x = current_x - old_center;
    int32_t offset_y = current_y - old_center;

    // 设置新的瞳孔大小
    lv_obj_set_size(pupil, value, value);

    // 计算新的中心位置并保持偏移量
    int32_t new_center = (200 - value) / 2;
    lv_obj_set_pos(pupil, new_center + offset_x, new_center + offset_y);
}

static void emotion_eyelid_top_cb(void *obj, int32_t value)
{
    lv_obj_t *eyelid = (lv_obj_t *)obj;
    lv_obj_set_height(eyelid, value);
}

static void emotion_eyelid_bottom_cb(void *obj, int32_t value)
{
    lv_obj_t *eyelid = (lv_obj_t *)obj;
    lv_obj_set_height(eyelid, value);
}

// 设置眼睛情绪 - 使用平滑动画切换
static void set_eye_emotion(eye_t *eye, eye_state_t emotion)
{
    if (eye->state == emotion) return;  // 避免重复设置

    // 获取当前瞳孔尺寸和眼睑高度
    int32_t current_pupil_size = lv_obj_get_width(eye->pupil);
    int32_t current_top_height = lv_obj_get_height(eye->eyelid_top);
    int32_t current_bottom_height = lv_obj_get_height(eye->eyelid_bottom);

    // 目标参数
    int32_t target_pupil_size = 100;
    int32_t target_top_height = 0;
    int32_t target_bottom_height = 0;

    switch (emotion) {
        case EYE_STATE_HAPPY:
            target_pupil_size = 110;
            target_bottom_height = 25;
            break;
        case EYE_STATE_ANGRY:
            target_pupil_size = 80;
            target_top_height = 35;
            break;
        case EYE_STATE_SURPRISED:
            target_pupil_size = 120;
            break;
        case EYE_STATE_SLEEPY:
            target_pupil_size = 90;
            target_top_height = 45;
            target_bottom_height = 45;
            break;
        default:
            // 正常状态已设置默认值
            break;
    }

    // 创建瞳孔尺寸动画
    if (current_pupil_size != target_pupil_size) {
        lv_anim_t pupil_anim;
        lv_anim_init(&pupil_anim);
        lv_anim_set_var(&pupil_anim, eye->pupil);
        lv_anim_set_values(&pupil_anim, current_pupil_size, target_pupil_size);
        lv_anim_set_time(&pupil_anim, 400);  // 400ms平滑过渡
        lv_anim_set_exec_cb(&pupil_anim, emotion_pupil_size_cb);
        lv_anim_set_path_cb(&pupil_anim, lv_anim_path_ease_in_out);
        lv_anim_start(&pupil_anim);
    }

    // 创建上眼睑动画
    if (current_top_height != target_top_height) {
        lv_anim_t top_anim;
        lv_anim_init(&top_anim);
        lv_anim_set_var(&top_anim, eye->eyelid_top);
        lv_anim_set_values(&top_anim, current_top_height, target_top_height);
        lv_anim_set_time(&top_anim, 400);
        lv_anim_set_exec_cb(&top_anim, emotion_eyelid_top_cb);
        lv_anim_set_path_cb(&top_anim, lv_anim_path_ease_in_out);
        lv_anim_start(&top_anim);
    }

    // 创建下眼睑动画
    if (current_bottom_height != target_bottom_height) {
        lv_anim_t bottom_anim;
        lv_anim_init(&bottom_anim);
        lv_anim_set_var(&bottom_anim, eye->eyelid_bottom);
        lv_anim_set_values(&bottom_anim, current_bottom_height, target_bottom_height);
        lv_anim_set_time(&bottom_anim, 400);
        lv_anim_set_exec_cb(&bottom_anim, emotion_eyelid_bottom_cb);
        lv_anim_set_path_cb(&bottom_anim, lv_anim_path_ease_in_out);
        lv_anim_start(&bottom_anim);
    }

    eye->state = emotion;
}

// 创建心跳效果 - 适应新的瞳孔尺寸
static void create_heartbeat_effect(eye_t *eye)
{
    lv_anim_t heartbeat_anim;
    lv_anim_init(&heartbeat_anim);
    lv_anim_set_var(&heartbeat_anim, eye->pupil);
    lv_anim_set_values(&heartbeat_anim, 100, 115);  // 从100到115，适应新尺寸
    lv_anim_set_time(&heartbeat_anim, 200);
    lv_anim_set_exec_cb(&heartbeat_anim, heartbeat_exec_cb);
    lv_anim_set_path_cb(&heartbeat_anim, lv_anim_path_ease_in_out);
    lv_anim_set_repeat_count(&heartbeat_anim, 2);
    lv_anim_set_playback_time(&heartbeat_anim, 200);
    lv_anim_start(&heartbeat_anim);
}

// 创建闪烁效果
static void create_sparkle_effect(eye_t *eye)
{
    // 创建临时闪烁点
    lv_obj_t *sparkle = lv_obj_create(eye->eye_container);
    lv_obj_set_size(sparkle, 5, 5);
    lv_obj_set_pos(sparkle, 30 + (esp_random() % 120), 30 + (esp_random() % 120));
    lv_obj_set_style_radius(sparkle, LV_RADIUS_CIRCLE, 0);
    lv_obj_set_style_bg_color(sparkle, lv_color_white(), 0);
    lv_obj_set_style_border_width(sparkle, 0, 0);

    // 闪烁动画
    lv_anim_t sparkle_anim;
    lv_anim_init(&sparkle_anim);
    lv_anim_set_var(&sparkle_anim, sparkle);
    lv_anim_set_values(&sparkle_anim, LV_OPA_TRANSP, LV_OPA_COVER);
    lv_anim_set_time(&sparkle_anim, 300);
    lv_anim_set_exec_cb(&sparkle_anim, sparkle_exec_cb);
    lv_anim_set_path_cb(&sparkle_anim, lv_anim_path_ease_in_out);
    lv_anim_set_repeat_count(&sparkle_anim, 1);
    lv_anim_set_playback_time(&sparkle_anim, 300);
    lv_anim_set_ready_cb(&sparkle_anim, sparkle_ready_cb);
    lv_anim_start(&sparkle_anim);
}

// 创建纯净的眼球UI - 纯黑白配色
static void create_rich_eye_ui(lv_disp_t *disp, eye_t *eye, const char* eye_name)
{
    ESP_LOGI(TAG, "👁️ 创建%s纯净眼球UI", eye_name);

    // 设置当前显示器
    lv_disp_set_default(disp);

    // 获取当前屏幕 - 纯黑背景
    lv_obj_t *scr = lv_disp_get_scr_act(disp);
    lv_obj_set_style_bg_color(scr, lv_color_black(), 0);

    // 创建眼球容器 - 纯白眼球，无边框无阴影
    eye->eye_container = lv_obj_create(scr);
    lv_obj_set_size(eye->eye_container, 200, 200);  // 稍微大一点
    lv_obj_center(eye->eye_container);
    lv_obj_set_style_radius(eye->eye_container, LV_RADIUS_CIRCLE, 0);
    lv_obj_set_style_bg_color(eye->eye_container, lv_color_white(), 0);  // 纯白
    lv_obj_set_style_border_width(eye->eye_container, 0, 0);  // 无边框
    lv_obj_set_style_shadow_width(eye->eye_container, 0, 0);  // 无阴影
    lv_obj_set_style_pad_all(eye->eye_container, 0, 0);  // 无内边距

    // 创建瞳孔 - 纯黑，更大尺寸
    eye->pupil = lv_obj_create(eye->eye_container);
    lv_obj_set_size(eye->pupil, 100, 100);  // 增大瞳孔尺寸
    lv_obj_set_pos(eye->pupil, 50, 50);  // 居中位置 (200-100)/2 = 50
    lv_obj_set_style_radius(eye->pupil, LV_RADIUS_CIRCLE, 0);
    lv_obj_set_style_bg_color(eye->pupil, lv_color_black(), 0);  // 纯黑
    lv_obj_set_style_border_width(eye->pupil, 0, 0);  // 无边框
    lv_obj_set_style_pad_all(eye->pupil, 0, 0);  // 无内边距

    // 创建统一的双高光点布局（左右眼相同）
    // 大高光点 - 在瞳孔中心点的左下区域
    eye->highlight = lv_obj_create(eye->pupil);
    lv_obj_set_size(eye->highlight, 25, 25);  // 大高光点
    lv_obj_set_pos(eye->highlight, 30, 55);  // 左下区域，靠近中心点
    lv_obj_set_style_radius(eye->highlight, LV_RADIUS_CIRCLE, 0);
    lv_obj_set_style_bg_color(eye->highlight, lv_color_white(), 0);
    lv_obj_set_style_border_width(eye->highlight, 0, 0);
    lv_obj_set_style_pad_all(eye->highlight, 0, 0);

    // 小高光点 - 在右上区域
    lv_obj_t *highlight_small = lv_obj_create(eye->pupil);
    lv_obj_set_size(highlight_small, 12, 12);  // 小高光点
    lv_obj_set_pos(highlight_small, 65, 20);  // 右上区域
    lv_obj_set_style_radius(highlight_small, LV_RADIUS_CIRCLE, 0);
    lv_obj_set_style_bg_color(highlight_small, lv_color_white(), 0);
    lv_obj_set_style_border_width(highlight_small, 0, 0);
    lv_obj_set_style_pad_all(highlight_small, 0, 0);

    // 创建上眼睑 - 纯黑
    eye->eyelid_top = lv_obj_create(scr);
    lv_obj_set_size(eye->eyelid_top, 200, 0);  // 初始高度为0
    lv_obj_align_to(eye->eyelid_top, eye->eye_container, LV_ALIGN_OUT_TOP_MID, 0, 0);
    lv_obj_set_style_bg_color(eye->eyelid_top, lv_color_black(), 0);  // 纯黑
    lv_obj_set_style_border_width(eye->eyelid_top, 0, 0);
    lv_obj_set_style_radius(eye->eyelid_top, 0, 0);
    lv_obj_set_style_pad_all(eye->eyelid_top, 0, 0);

    // 创建下眼睑 - 纯黑
    eye->eyelid_bottom = lv_obj_create(scr);
    lv_obj_set_size(eye->eyelid_bottom, 200, 0);  // 初始高度为0
    lv_obj_align_to(eye->eyelid_bottom, eye->eye_container, LV_ALIGN_OUT_BOTTOM_MID, 0, 0);
    lv_obj_set_style_bg_color(eye->eyelid_bottom, lv_color_black(), 0);  // 纯黑
    lv_obj_set_style_border_width(eye->eyelid_bottom, 0, 0);
    lv_obj_set_style_radius(eye->eyelid_bottom, 0, 0);
    lv_obj_set_style_pad_all(eye->eyelid_bottom, 0, 0);

    // 初始化眼睛状态
    eye->state = EYE_STATE_NORMAL;
    eye->pupil_x = 0;
    eye->pupil_y = 0;
    // 初始化动画状态跟踪
    eye->start_x = 0;
    eye->start_y = 0;
    eye->target_x = 0;
    eye->target_y = 0;
    eye->is_left_eye = (eye == &left_eye);

    ESP_LOGI(TAG, "✅ %s纯净眼球UI创建完成", eye_name);
}

// 眼睛动画定时器回调
static void eye_animation_timer_cb(lv_timer_t *timer)
{
    static uint32_t animation_counter = 0;
    static uint32_t last_blink_time = 0;
    static uint32_t last_look_time = 0;
    static uint32_t last_emotion_time = 0;

    uint32_t current_time = lv_tick_get();
    animation_counter++;

    // 随机眨眼 (每3-6秒，更自然)
    if (current_time - last_blink_time > (3000 + (esp_random() % 3000))) {
        if (left_eye.state == EYE_STATE_NORMAL && right_eye.state == EYE_STATE_NORMAL) {
            start_blink_animation(&left_eye);
            start_blink_animation(&right_eye);
            last_blink_time = current_time;
            ESP_LOGI(TAG, "👁️ 执行眨眼动画");
        }
    }

    // 随机眼球移动 (每3-7秒，平滑移动)
    if (current_time - last_look_time > (3000 + (esp_random() % 4000))) {
        if (left_eye.state == EYE_STATE_NORMAL && right_eye.state == EYE_STATE_NORMAL) {
            // 生成随机目标位置，范围适中
            int32_t target_x = (esp_random() % 24) - 12;  // -12 到 +12
            int32_t target_y = (esp_random() % 24) - 12;  // -12 到 +12

            start_look_animation(&left_eye, target_x, target_y);
            start_look_animation(&right_eye, target_x, target_y);
            last_look_time = current_time;
            ESP_LOGI(TAG, "👁️ 平滑移动到 (%d, %d)", (int)target_x, (int)target_y);
        }
    }

    // 随机情绪变化 (每10-20秒)
    if (current_time - last_emotion_time > (10000 + (esp_random() % 10000))) {
        if (left_eye.state == EYE_STATE_NORMAL && right_eye.state == EYE_STATE_NORMAL) {
            eye_state_t emotions[] = {EYE_STATE_HAPPY, EYE_STATE_SURPRISED, EYE_STATE_SLEEPY};
            eye_state_t emotion = emotions[esp_random() % 3];

            set_eye_emotion(&left_eye, emotion);
            set_eye_emotion(&right_eye, emotion);
            last_emotion_time = current_time;

            // 情绪切换日志 - 暂时注释避免崩溃
            /*
            switch (emotion) {
                case EYE_STATE_HAPPY:
                    ESP_LOGI(TAG, "😊 切换到开心情绪");
                    break;
                case EYE_STATE_SURPRISED:
                    ESP_LOGI(TAG, "� 切换到惊讶情绪");
                    break;
                case EYE_STATE_SLEEPY:
                    ESP_LOGI(TAG, "😴 切换到困倦情绪");
                    break;
                default:
                    ESP_LOGI(TAG, "😊 切换情绪");
                    break;
            */
            ESP_LOGI(TAG, "情绪切换: %d", (int)emotion);

            // 2秒后恢复正常
            lv_timer_t *restore_timer = lv_timer_create(restore_emotion_cb, 2000, NULL);
            lv_timer_set_repeat_count(restore_timer, 1);
        }
    }

    // 简化特效，减少复杂动画以提高稳定性
    // 心跳效果 (每20秒，降低频率)
    if (animation_counter % 200 == 100) {  // 20秒偏移
        if (left_eye.state == EYE_STATE_NORMAL && right_eye.state == EYE_STATE_NORMAL) {
            create_heartbeat_effect(&left_eye);
            create_heartbeat_effect(&right_eye);
            ESP_LOGI(TAG, "💓 心跳效果");
        }
    }

    // 特殊动画序列 (每30秒)
    if (animation_counter % 300 == 0) {  // 30秒 = 300 * 100ms
        ESP_LOGI(TAG, "🎭 执行特殊动画序列");

        // 序列1：左看
        lv_timer_t *seq_timer1 = lv_timer_create(seq_look_left_cb, 500, NULL);
        lv_timer_set_repeat_count(seq_timer1, 1);

        // 序列2：右看
        lv_timer_t *seq_timer2 = lv_timer_create(seq_look_right_cb, 1500, NULL);
        lv_timer_set_repeat_count(seq_timer2, 1);

        // 序列3：向上看
        lv_timer_t *seq_timer3 = lv_timer_create(seq_look_up_cb, 2500, NULL);
        lv_timer_set_repeat_count(seq_timer3, 1);

        // 序列4：回中心
        lv_timer_t *seq_timer4 = lv_timer_create(seq_look_center_cb, 3500, NULL);
        lv_timer_set_repeat_count(seq_timer4, 1);

        // 序列5：眨眼
        lv_timer_t *seq_timer5 = lv_timer_create(seq_blink_cb, 4500, NULL);
        lv_timer_set_repeat_count(seq_timer5, 1);
    }

    // 超级特殊序列 (每60秒)
    if (animation_counter % 600 == 300) {  // 60秒，偏移30秒
        ESP_LOGI(TAG, "🌟 执行超级特殊序列");

        // 愤怒表情
        lv_timer_t *super_timer1 = lv_timer_create(super_angry_cb, 1000, NULL);
        lv_timer_set_repeat_count(super_timer1, 1);

        // 惊讶表情
        lv_timer_t *super_timer2 = lv_timer_create(super_surprised_cb, 3000, NULL);
        lv_timer_set_repeat_count(super_timer2, 1);

        // 开心表情
        lv_timer_t *super_timer3 = lv_timer_create(super_happy_cb, 5000, NULL);
        lv_timer_set_repeat_count(super_timer3, 1);

        // 恢复正常
        lv_timer_t *super_timer4 = lv_timer_create(super_normal_cb, 7000, NULL);
        lv_timer_set_repeat_count(super_timer4, 1);
    }
}

// LVGL任务
static void lvgl_task(void *arg)
{
    ESP_LOGI(TAG, "🚀 启动LVGL任务");

    // 等待显示器初始化完成
    vTaskDelay(pdMS_TO_TICKS(100));

    // 创建丰富的双眼UI
    create_rich_eye_ui(left_disp, &left_eye, "左");
    create_rich_eye_ui(right_disp, &right_eye, "右");

    // 创建眼睛动画定时器 (每200ms执行一次，降低频率提高稳定性)
    eye_animation_timer = lv_timer_create(eye_animation_timer_cb, 200, NULL);

    ESP_LOGI(TAG, "🎉 纯净双眼UI创建完成，开始LVGL主循环");
    ESP_LOGI(TAG, "👁️ 眼睛动画系统已启动：");
    ESP_LOGI(TAG, "   - 🔄 自动眨眼：每3-6秒");
    ESP_LOGI(TAG, "   - 👀 眼球移动：每4-10秒");
    ESP_LOGI(TAG, "   - 😊 情绪变化：每10-20秒");
    ESP_LOGI(TAG, "   - 💓 心跳效果：每20秒");
    ESP_LOGI(TAG, "   - 🎭 特殊序列：每30秒");
    ESP_LOGI(TAG, "   - 🌟 超级序列：每60秒");

    uint32_t task_delay_ms = LVGL_TASK_MAX_DELAY_MS;
    while (1) {
        // 获取互斥锁并处理LVGL
        if (lvgl_lock(-1)) {
            task_delay_ms = lv_timer_handler();
            lvgl_unlock();
        }

        // 限制任务延迟范围
        if (task_delay_ms > LVGL_TASK_MAX_DELAY_MS) {
            task_delay_ms = LVGL_TASK_MAX_DELAY_MS;
        } else if (task_delay_ms < LVGL_TASK_MIN_DELAY_MS) {
            task_delay_ms = LVGL_TASK_MIN_DELAY_MS;
        }

        vTaskDelay(pdMS_TO_TICKS(task_delay_ms));
    }
}

void app_main(void)
{
    ESP_LOGI(TAG, "🚀 ESP32双眼显示系统启动 - 基于lvgl_demo.c架构");
    
    // 1. 初始化SPI总线
    ESP_LOGI(TAG, "🚌 初始化SPI总线");
    const spi_bus_config_t bus_cfg = GC9A01_PANEL_BUS_SPI_CONFIG(PIN_SCK, PIN_MOSI, SCREEN_WIDTH * SCREEN_HEIGHT);
    ESP_ERROR_CHECK(spi_bus_initialize(LCD_HOST, &bus_cfg, SPI_DMA_CH_AUTO));
    
    // 2. 初始化LVGL
    ESP_LOGI(TAG, "📚 初始化LVGL库");
    lv_init();
    
    // 3. 为左屏分配显示缓冲区
    ESP_LOGI(TAG, "💾 为左屏分配显示缓冲区");
    static lv_disp_draw_buf_t left_disp_buf;
    static lv_disp_drv_t left_disp_drv;
    lv_color_t *left_buf1 = heap_caps_malloc(SCREEN_WIDTH * LVGL_BUFFER_HEIGHT * sizeof(lv_color_t), MALLOC_CAP_DMA);
    lv_color_t *left_buf2 = heap_caps_malloc(SCREEN_WIDTH * LVGL_BUFFER_HEIGHT * sizeof(lv_color_t), MALLOC_CAP_DMA);
    assert(left_buf1 && left_buf2);
    lv_disp_draw_buf_init(&left_disp_buf, left_buf1, left_buf2, SCREEN_WIDTH * LVGL_BUFFER_HEIGHT);
    
    // 4. 初始化左屏
    ESP_ERROR_CHECK(init_gc9a01_panel(PIN_CS_LEFT, &left_panel_handle, &left_disp_drv, 
                                      notify_left_flush_ready, left_flush_cb));
    left_disp_drv.draw_buf = &left_disp_buf;
    left_disp = lv_disp_drv_register(&left_disp_drv);
    
    // 5. 为右屏分配显示缓冲区
    ESP_LOGI(TAG, "💾 为右屏分配显示缓冲区");
    static lv_disp_draw_buf_t right_disp_buf;
    static lv_disp_drv_t right_disp_drv;
    lv_color_t *right_buf1 = heap_caps_malloc(SCREEN_WIDTH * LVGL_BUFFER_HEIGHT * sizeof(lv_color_t), MALLOC_CAP_DMA);
    lv_color_t *right_buf2 = heap_caps_malloc(SCREEN_WIDTH * LVGL_BUFFER_HEIGHT * sizeof(lv_color_t), MALLOC_CAP_DMA);
    assert(right_buf1 && right_buf2);
    lv_disp_draw_buf_init(&right_disp_buf, right_buf1, right_buf2, SCREEN_WIDTH * LVGL_BUFFER_HEIGHT);
    
    // 6. 初始化右屏
    ESP_ERROR_CHECK(init_gc9a01_panel(PIN_CS_RIGHT, &right_panel_handle, &right_disp_drv, 
                                      notify_right_flush_ready, right_flush_cb));
    right_disp_drv.draw_buf = &right_disp_buf;
    right_disp = lv_disp_drv_register(&right_disp_drv);
    
    // 7. 创建LVGL定时器
    ESP_LOGI(TAG, "⏰ 创建LVGL定时器");
    const esp_timer_create_args_t lvgl_tick_timer_args = {
        .callback = &increase_lvgl_tick,
        .name = "lvgl_tick"
    };
    esp_timer_handle_t lvgl_tick_timer = NULL;
    ESP_ERROR_CHECK(esp_timer_create(&lvgl_tick_timer_args, &lvgl_tick_timer));
    ESP_ERROR_CHECK(esp_timer_start_periodic(lvgl_tick_timer, LVGL_TICK_PERIOD_MS * 1000));
    
    // 8. 创建LVGL任务和互斥锁
    ESP_LOGI(TAG, "🔒 创建LVGL互斥锁和任务");
    lvgl_mux = xSemaphoreCreateMutex();
    assert(lvgl_mux);
    xTaskCreate(lvgl_task, "LVGL", LVGL_TASK_STACK_SIZE, NULL, LVGL_TASK_PRIORITY, NULL);
    
    ESP_LOGI(TAG, "🎉 系统初始化完成！纯净双眼显示系统正在运行...");
    ESP_LOGI(TAG, "🌟 功能特性：");
    ESP_LOGI(TAG, "   ✨ 自动眨眼动画 - 自然眨眼节奏");
    ESP_LOGI(TAG, "   👀 平滑眼球追踪 - 优化移动算法");
    ESP_LOGI(TAG, "   😊 多种情绪表达 - 纯黑白配色");
    ESP_LOGI(TAG, "   🎭 复杂动画序列 - 左右上下看+眨眼组合");
    ESP_LOGI(TAG, "   💫 纯净高光效果 - 单一高光点");
    ESP_LOGI(TAG, "   🎨 眼睑动画 - 上下眼睑协调");
    ESP_LOGI(TAG, "   💓 心跳效果 - 瞳孔律动");
    ESP_LOGI(TAG, "   🖤 纯黑白配色 - 去除杂质");
    ESP_LOGI(TAG, "   ⚡ 优化性能 - 降低动画频率");
}
